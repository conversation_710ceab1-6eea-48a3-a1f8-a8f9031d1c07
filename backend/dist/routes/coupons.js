"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Get all coupons (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    const where = {};
    if (isActive !== undefined) {
        where.isActive = isActive === 'true';
    }
    const total = await prisma.coupon.count({ where });
    const coupons = await prisma.coupon.findMany({
        where,
        skip: offset,
        take: limitNum,
        orderBy: { [sortBy]: sortOrder }
    });
    const pagination = (0, shared_1.calculatePagination)(pageNum, limitNum, total);
    res.json((0, shared_1.createApiResponse)(true, {
        coupons,
        pagination
    }));
}));
// Validate coupon (public)
router.post('/validate', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { code, orderAmount } = req.body;
    if (!code) {
        throw new shared_1.AppError('Coupon code is required', 400);
    }
    const coupon = await prisma.coupon.findFirst({
        where: {
            code: code.toUpperCase(),
            isActive: true,
            OR: [
                { expiresAt: null },
                { expiresAt: { gt: new Date() } }
            ]
        }
    });
    if (!coupon) {
        throw new shared_1.AppError('Invalid or expired coupon code', 400);
    }
    if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
        throw new shared_1.AppError('Coupon usage limit exceeded', 400);
    }
    if (coupon.minOrderAmount && orderAmount && orderAmount < coupon.minOrderAmount) {
        throw new shared_1.AppError(`Minimum order amount for this coupon is ${coupon.minOrderAmount}`, 400);
    }
    res.json((0, shared_1.createApiResponse)(true, { coupon }, 'Coupon is valid'));
}));
// Create coupon (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, validation_1.validate)(validation_1.schemas.createCoupon), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const couponData = req.body;
    // Generate code if not provided
    if (!couponData.code) {
        couponData.code = (0, shared_1.generateCouponCode)();
    }
    else {
        couponData.code = couponData.code.toUpperCase();
    }
    // Check if code already exists
    const existingCoupon = await prisma.coupon.findUnique({
        where: { code: couponData.code }
    });
    if (existingCoupon) {
        throw new shared_1.AppError('Coupon code already exists', 409);
    }
    const coupon = await prisma.coupon.create({
        data: couponData
    });
    res.status(201).json((0, shared_1.createApiResponse)(true, { coupon }, 'Coupon created successfully'));
}));
// Update coupon (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    if (updateData.code) {
        updateData.code = updateData.code.toUpperCase();
        // Check if new code already exists
        const existingCoupon = await prisma.coupon.findFirst({
            where: {
                code: updateData.code,
                id: { not: id }
            }
        });
        if (existingCoupon) {
            throw new shared_1.AppError('Coupon code already exists', 409);
        }
    }
    const coupon = await prisma.coupon.update({
        where: { id },
        data: updateData
    });
    res.json((0, shared_1.createApiResponse)(true, { coupon }, 'Coupon updated successfully'));
}));
// Delete coupon (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    await prisma.coupon.delete({
        where: { id }
    });
    res.json((0, shared_1.createApiResponse)(true, null, 'Coupon deleted successfully'));
}));
// Generate random coupon code (admin only)
router.post('/generate-code', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    let code;
    let isUnique = false;
    // Generate unique code
    while (!isUnique) {
        code = (0, shared_1.generateCouponCode)();
        const existingCoupon = await prisma.coupon.findUnique({
            where: { code }
        });
        if (!existingCoupon) {
            isUnique = true;
        }
    }
    res.json((0, shared_1.createApiResponse)(true, { code }, 'Coupon code generated'));
}));
exports.default = router;
//# sourceMappingURL=coupons.js.map