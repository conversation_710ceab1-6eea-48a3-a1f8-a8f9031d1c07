"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Get orders (admin gets all, users get their own)
router.get('/', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, status, userId, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    // Build where clause
    const where = {};
    // Non-admin users can only see their own orders
    if (req.user.role !== shared_1.UserRole.ADMIN) {
        where.userId = req.user.id;
    }
    else if (userId) {
        where.userId = userId;
    }
    if (status) {
        where.status = status;
    }
    // Get total count
    const total = await prisma.order.count({ where });
    // Get orders
    const orders = await prisma.order.findMany({
        where,
        skip: offset,
        take: limitNum,
        orderBy: { [sortBy]: sortOrder },
        include: {
            user: {
                select: {
                    id: true,
                    email: true,
                    username: true
                }
            },
            items: {
                include: {
                    product: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            thumbnailUrl: true
                        }
                    }
                }
            }
        }
    });
    const pagination = (0, shared_1.calculatePagination)(pageNum, limitNum, total);
    res.json((0, shared_1.createApiResponse)(true, {
        orders,
        pagination
    }));
}));
// Get single order
router.get('/:id', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const where = { id };
    // Non-admin users can only see their own orders
    if (req.user.role !== shared_1.UserRole.ADMIN) {
        where.userId = req.user.id;
    }
    const order = await prisma.order.findFirst({
        where,
        include: {
            user: {
                select: {
                    id: true,
                    email: true,
                    username: true
                }
            },
            items: {
                include: {
                    product: true
                }
            }
        }
    });
    if (!order) {
        throw new shared_1.AppError('Order not found', 404);
    }
    res.json((0, shared_1.createApiResponse)(true, { order }));
}));
// Create order
router.post('/', auth_1.authenticate, (0, validation_1.validate)(validation_1.schemas.createOrder), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { items, paymentMethod, couponCode } = req.body;
    const userId = req.user.id;
    // Validate products and calculate total
    let totalAmount = 0;
    const orderItems = [];
    for (const item of items) {
        const product = await prisma.product.findFirst({
            where: {
                id: item.productId,
                isActive: true
            }
        });
        if (!product) {
            throw new shared_1.AppError(`Product ${item.productId} not found or inactive`, 400);
        }
        const itemTotal = product.price * item.quantity;
        totalAmount += itemTotal;
        orderItems.push({
            productId: product.id,
            quantity: item.quantity,
            price: product.price
        });
    }
    // Apply coupon if provided
    let discountAmount = 0;
    if (couponCode) {
        const coupon = await prisma.coupon.findFirst({
            where: {
                code: couponCode,
                isActive: true,
                OR: [
                    { expiresAt: null },
                    { expiresAt: { gt: new Date() } }
                ]
            }
        });
        if (!coupon) {
            throw new shared_1.AppError('Invalid or expired coupon code', 400);
        }
        if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
            throw new shared_1.AppError('Coupon usage limit exceeded', 400);
        }
        if (coupon.minOrderAmount && totalAmount < coupon.minOrderAmount) {
            throw new shared_1.AppError(`Minimum order amount for this coupon is ${coupon.minOrderAmount}`, 400);
        }
        discountAmount = (0, shared_1.calculateDiscount)(totalAmount, coupon.discountType, coupon.discountValue);
        // Update coupon usage
        await prisma.coupon.update({
            where: { id: coupon.id },
            data: { usedCount: { increment: 1 } }
        });
    }
    const finalAmount = totalAmount - discountAmount;
    // Get payment address for the selected method
    const wallet = await prisma.cryptoWallet.findFirst({
        where: {
            currency: paymentMethod,
            isActive: true
        }
    });
    if (!wallet) {
        throw new shared_1.AppError(`No active wallet found for ${paymentMethod}`, 400);
    }
    // Create order
    const order = await prisma.order.create({
        data: {
            userId,
            totalAmount: finalAmount,
            paymentMethod,
            paymentAddress: wallet.address,
            couponCode,
            discountAmount,
            items: {
                create: orderItems
            }
        },
        include: {
            items: {
                include: {
                    product: true
                }
            }
        }
    });
    res.status(201).json((0, shared_1.createApiResponse)(true, { order }, 'Order created successfully'));
}));
// Update order status (admin only)
router.put('/:id/status', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { status, transactionHash } = req.body;
    if (!Object.values(shared_1.OrderStatus).includes(status)) {
        throw new shared_1.AppError('Invalid order status', 400);
    }
    const order = await prisma.order.findUnique({
        where: { id },
        include: {
            items: {
                include: {
                    product: true
                }
            },
            user: true
        }
    });
    if (!order) {
        throw new shared_1.AppError('Order not found', 404);
    }
    const updateData = { status };
    if (transactionHash) {
        updateData.transactionHash = transactionHash;
    }
    const updatedOrder = await prisma.order.update({
        where: { id },
        data: updateData
    });
    // If order is marked as paid, create download links
    if (status === shared_1.OrderStatus.PAID) {
        const downloads = order.items.map(item => ({
            userId: order.userId,
            productId: item.productId,
            downloadUrl: `/api/downloads/${item.productId}/${order.userId}`,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        }));
        await prisma.download.createMany({
            data: downloads
        });
        // Update order status to delivered
        await prisma.order.update({
            where: { id },
            data: { status: shared_1.OrderStatus.DELIVERED }
        });
    }
    res.json((0, shared_1.createApiResponse)(true, { order: updatedOrder }, 'Order status updated successfully'));
}));
// Cancel order
router.put('/:id/cancel', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const where = { id };
    // Non-admin users can only cancel their own orders
    if (req.user.role !== shared_1.UserRole.ADMIN) {
        where.userId = req.user.id;
    }
    const order = await prisma.order.findFirst({
        where
    });
    if (!order) {
        throw new shared_1.AppError('Order not found', 404);
    }
    if (order.status !== shared_1.OrderStatus.PENDING) {
        throw new shared_1.AppError('Only pending orders can be cancelled', 400);
    }
    const updatedOrder = await prisma.order.update({
        where: { id },
        data: { status: shared_1.OrderStatus.CANCELLED }
    });
    res.json((0, shared_1.createApiResponse)(true, { order: updatedOrder }, 'Order cancelled successfully'));
}));
// Get order statistics (admin only)
router.get('/stats/overview', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const [totalOrders, pendingOrders, paidOrders, totalRevenue, todayOrders, todayRevenue] = await Promise.all([
        prisma.order.count(),
        prisma.order.count({ where: { status: shared_1.OrderStatus.PENDING } }),
        prisma.order.count({ where: { status: shared_1.OrderStatus.PAID } }),
        prisma.order.aggregate({
            where: { status: { in: [shared_1.OrderStatus.PAID, shared_1.OrderStatus.DELIVERED] } },
            _sum: { totalAmount: true }
        }),
        prisma.order.count({
            where: {
                createdAt: {
                    gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
            }
        }),
        prisma.order.aggregate({
            where: {
                status: { in: [shared_1.OrderStatus.PAID, shared_1.OrderStatus.DELIVERED] },
                createdAt: {
                    gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
            },
            _sum: { totalAmount: true }
        })
    ]);
    const stats = {
        totalOrders,
        pendingOrders,
        paidOrders,
        totalRevenue: totalRevenue._sum.totalAmount || 0,
        todayOrders,
        todayRevenue: todayRevenue._sum.totalAmount || 0
    };
    res.json((0, shared_1.createApiResponse)(true, { stats }));
}));
exports.default = router;
//# sourceMappingURL=orders.js.map