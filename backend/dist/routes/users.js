"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Get all users (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, role, isActive, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    const where = {};
    if (role) {
        where.role = role;
    }
    if (isActive !== undefined) {
        where.isActive = isActive === 'true';
    }
    if (search) {
        where.OR = [
            { email: { contains: search, mode: 'insensitive' } },
            { username: { contains: search, mode: 'insensitive' } }
        ];
    }
    const total = await prisma.user.count({ where });
    const users = await prisma.user.findMany({
        where,
        skip: offset,
        take: limitNum,
        orderBy: { [sortBy]: sortOrder },
        select: {
            id: true,
            email: true,
            username: true,
            telegramId: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
            _count: {
                select: {
                    orders: true
                }
            }
        }
    });
    const pagination = (0, shared_1.calculatePagination)(pageNum, limitNum, total);
    res.json((0, shared_1.createApiResponse)(true, {
        users,
        pagination
    }));
}));
// Get single user (admin only or own profile)
router.get('/:id', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    // Users can only access their own profile unless they're admin
    if (req.user.role !== shared_1.UserRole.ADMIN && req.user.id !== id) {
        throw new shared_1.AppError('Access denied', 403);
    }
    const user = await prisma.user.findUnique({
        where: { id },
        select: {
            id: true,
            email: true,
            username: true,
            telegramId: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
            _count: {
                select: {
                    orders: true,
                    downloads: true
                }
            }
        }
    });
    if (!user) {
        throw new shared_1.AppError('User not found', 404);
    }
    res.json((0, shared_1.createApiResponse)(true, { user }));
}));
// Update user (admin only or own profile)
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)(validation_1.schemas.updateUser), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    // Users can only update their own profile unless they're admin
    if (req.user.role !== shared_1.UserRole.ADMIN && req.user.id !== id) {
        throw new shared_1.AppError('Access denied', 403);
    }
    // Non-admin users cannot change role or isActive status
    if (req.user.role !== shared_1.UserRole.ADMIN) {
        delete updateData.role;
        delete updateData.isActive;
    }
    // Check if email is already taken
    if (updateData.email) {
        const existingUser = await prisma.user.findFirst({
            where: {
                email: updateData.email,
                id: { not: id }
            }
        });
        if (existingUser) {
            throw new shared_1.AppError('Email already in use', 409);
        }
    }
    const user = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
            id: true,
            email: true,
            username: true,
            telegramId: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true
        }
    });
    res.json((0, shared_1.createApiResponse)(true, { user }, 'User updated successfully'));
}));
// Delete user (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    // Prevent admin from deleting themselves
    if (req.user.id === id) {
        throw new shared_1.AppError('Cannot delete your own account', 400);
    }
    const user = await prisma.user.findUnique({
        where: { id }
    });
    if (!user) {
        throw new shared_1.AppError('User not found', 404);
    }
    // Check if user has any orders
    const orderCount = await prisma.order.count({
        where: { userId: id }
    });
    if (orderCount > 0) {
        throw new shared_1.AppError('Cannot delete user with existing orders', 400);
    }
    await prisma.user.delete({
        where: { id }
    });
    res.json((0, shared_1.createApiResponse)(true, null, 'User deleted successfully'));
}));
// Get user statistics (admin only)
router.get('/stats/overview', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const [totalUsers, activeUsers, adminUsers, customerUsers, todayRegistrations] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        prisma.user.count({ where: { role: shared_1.UserRole.ADMIN } }),
        prisma.user.count({ where: { role: shared_1.UserRole.CUSTOMER } }),
        prisma.user.count({
            where: {
                createdAt: {
                    gte: new Date(new Date().setHours(0, 0, 0, 0))
                }
            }
        })
    ]);
    const stats = {
        totalUsers,
        activeUsers,
        adminUsers,
        customerUsers,
        todayRegistrations
    };
    res.json((0, shared_1.createApiResponse)(true, { stats }));
}));
// Link Telegram account
router.post('/:id/link-telegram', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { telegramId } = req.body;
    // Users can only link their own account unless they're admin
    if (req.user.role !== shared_1.UserRole.ADMIN && req.user.id !== id) {
        throw new shared_1.AppError('Access denied', 403);
    }
    if (!telegramId) {
        throw new shared_1.AppError('Telegram ID is required', 400);
    }
    // Check if Telegram ID is already linked to another account
    const existingUser = await prisma.user.findFirst({
        where: {
            telegramId: telegramId.toString(),
            id: { not: id }
        }
    });
    if (existingUser) {
        throw new shared_1.AppError('Telegram account already linked to another user', 409);
    }
    const user = await prisma.user.update({
        where: { id },
        data: { telegramId: telegramId.toString() },
        select: {
            id: true,
            email: true,
            username: true,
            telegramId: true,
            role: true,
            isActive: true
        }
    });
    res.json((0, shared_1.createApiResponse)(true, { user }, 'Telegram account linked successfully'));
}));
exports.default = router;
//# sourceMappingURL=users.js.map