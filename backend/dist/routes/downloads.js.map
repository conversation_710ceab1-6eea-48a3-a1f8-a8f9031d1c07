{"version": 3, "file": "downloads.js", "sourceRoot": "", "sources": ["../../src/routes/downloads.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,gDAAwB;AACxB,4CAAoB;AACpB,2CAA8C;AAC9C,6CAA+D;AAC/D,6DAA0D;AAC1D,8CAAqF;AAErF,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAChC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC,+CAA+C;AAC/C,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,mBAAY,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IAC7F,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAE5B,2CAA2C;IAC3C,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;QACzC,KAAK,EAAE;YACL,MAAM;YACN,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;YACrC,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,SAAS;iBACV;aACF;SACF;QACD,OAAO,EAAE;YACP,KAAK,EAAE;gBACL,KAAK,EAAE,EAAE,SAAS,EAAE;gBACpB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aAC3B;SACF;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,iBAAQ,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAEvC,+BAA+B;IAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACnI,MAAM,IAAI,iBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAED,iCAAiC;IACjC,IAAI,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC7C,KAAK,EAAE;YACL,MAAM;YACN,SAAS;YACT,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;SAC9B;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,6BAA6B;QAC7B,MAAM,aAAa,GAAG,IAAA,4BAAmB,EAAC,EAAE,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW;QAEzE,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,IAAI,EAAE;gBACJ,MAAM;gBACN,SAAS;gBACT,WAAW,EAAE,uBAAuB,aAAa,EAAE;gBACnD,SAAS;aACV;SACF,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE;QAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW;QACjC,SAAS,EAAE,QAAQ,CAAC,SAAS;QAC7B,OAAO,EAAE;YACP,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B;KACF,EAAE,sCAAsC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC;AAEJ,2BAA2B;AAC3B,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACzD,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAE7B,gCAAgC;IAChC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/C,KAAK,EAAE;YACL,WAAW,EAAE,uBAAuB,KAAK,EAAE;YAC3C,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;SAC9B;QACD,OAAO,EAAE;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;SACX;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,iBAAQ,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IACjC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;IAE5G,uBAAuB;IACvB,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,iBAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,gCAAgC;IAChC,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,yBAAyB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IACnF,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;IAC1D,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAElD,0BAA0B;IAC1B,MAAM,UAAU,GAAG,YAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACjD,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAErB,eAAe;IACf,OAAO,CAAC,GAAG,CAAC,oBAAoB,OAAO,CAAC,IAAI,YAAY,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AACjF,CAAC,CAAC,CAAC,CAAC;AAEJ,8BAA8B;AAC9B,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAY,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IAChF,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAC5B,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IAE3C,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAc,CAAC,CAAC;IACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAe,CAAC,CAAC;IAC3C,MAAM,MAAM,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IAExC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC/C,KAAK,EAAE,EAAE,MAAM,EAAE;QACjB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;QAC9B,OAAO,EAAE;YACP,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;oBACd,YAAY,EAAE,IAAI;iBACnB;aACF;SACF;KACF,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;IAEjE,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE;QAC/B,SAAS;QACT,UAAU,EAAE;YACV,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,QAAQ;YACf,KAAK;YACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACxC;KACF,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC,CAAC;AAEJ,0CAA0C;AAC1C,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,mBAAY,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IACzF,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAE5B,2CAA2C;IAC3C,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;QACzC,KAAK,EAAE;YACL,MAAM;YACN,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;YACrC,KAAK,EAAE;gBACL,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB;SACF;KACF,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC;IAE5B,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/C,KAAK,EAAE;YACL,MAAM;YACN,SAAS;YACT,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;SAC9B;KACF,CAAC,CAAC;IAEH,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE;QAC/B,WAAW;QACX,gBAAgB,EAAE,CAAC,CAAC,QAAQ;QAC5B,WAAW,EAAE,QAAQ,EAAE,WAAW;QAClC,SAAS,EAAE,QAAQ,EAAE,SAAS;KAC/B,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC,CAAC;AAEJ,kBAAe,MAAM,CAAC"}