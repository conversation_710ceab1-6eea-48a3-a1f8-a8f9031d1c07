"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const client_1 = require("@prisma/client");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Generate JWT tokens
const generateTokens = (userId) => {
    const accessToken = jsonwebtoken_1.default.sign({ userId }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: process.env.JWT_EXPIRES_IN || '15m' });
    const refreshToken = jsonwebtoken_1.default.sign({ userId }, process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key', { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' });
    return { accessToken, refreshToken };
};
// Register
router.post('/register', (0, validation_1.validate)(validation_1.schemas.register), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, username } = req.body;
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
        where: { email }
    });
    if (existingUser) {
        throw new shared_1.AppError('User with this email already exists', 409);
    }
    // Hash password
    const hashedPassword = await bcryptjs_1.default.hash(password, 12);
    // Create user
    const user = await prisma.user.create({
        data: {
            email,
            password: hashedPassword,
            username
        },
        select: {
            id: true,
            email: true,
            username: true,
            role: true,
            createdAt: true
        }
    });
    // Generate tokens
    const tokens = generateTokens(user.id);
    res.status(201).json((0, shared_1.createApiResponse)(true, {
        user,
        tokens
    }, 'User registered successfully'));
}));
// Login
router.post('/login', (0, validation_1.validate)(validation_1.schemas.login), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    // Find user
    const user = await prisma.user.findUnique({
        where: { email }
    });
    if (!user || !user.password) {
        throw new shared_1.AppError('Invalid email or password', 401);
    }
    // Check password
    const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
    if (!isPasswordValid) {
        throw new shared_1.AppError('Invalid email or password', 401);
    }
    if (!user.isActive) {
        throw new shared_1.AppError('Account is deactivated', 401);
    }
    // Generate tokens
    const tokens = generateTokens(user.id);
    res.json((0, shared_1.createApiResponse)(true, {
        user: {
            id: user.id,
            email: user.email,
            username: user.username,
            role: user.role,
            createdAt: user.createdAt
        },
        tokens
    }, 'Login successful'));
}));
// Refresh token
router.post('/refresh', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        throw new shared_1.AppError('Refresh token is required', 400);
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key');
        const user = await prisma.user.findUnique({
            where: { id: decoded.userId },
            select: { id: true, isActive: true }
        });
        if (!user || !user.isActive) {
            throw new shared_1.AppError('Invalid refresh token', 401);
        }
        const tokens = generateTokens(user.id);
        res.json((0, shared_1.createApiResponse)(true, { tokens }, 'Tokens refreshed successfully'));
    }
    catch (error) {
        throw new shared_1.AppError('Invalid refresh token', 401);
    }
}));
// Get current user
router.get('/me', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            email: true,
            username: true,
            telegramId: true,
            role: true,
            isActive: true,
            createdAt: true,
            updatedAt: true
        }
    });
    res.json((0, shared_1.createApiResponse)(true, { user }));
}));
// Change password
router.put('/change-password', auth_1.authenticate, (0, validation_1.validate)(validation_1.schemas.changePassword), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;
    const user = await prisma.user.findUnique({
        where: { id: userId }
    });
    if (!user || !user.password) {
        throw new shared_1.AppError('User not found', 404);
    }
    // Verify current password
    const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
        throw new shared_1.AppError('Current password is incorrect', 400);
    }
    // Hash new password
    const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
    // Update password
    await prisma.user.update({
        where: { id: userId },
        data: { password: hashedNewPassword }
    });
    res.json((0, shared_1.createApiResponse)(true, null, 'Password changed successfully'));
}));
// Logout (client-side token removal)
router.post('/logout', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // In a more sophisticated setup, you might want to blacklist the token
    res.json((0, shared_1.createApiResponse)(true, null, 'Logged out successfully'));
}));
exports.default = router;
//# sourceMappingURL=auth.js.map