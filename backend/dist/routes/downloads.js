"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Generate download link for purchased product
router.post('/generate/:productId', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId } = req.params;
    const userId = req.user.id;
    // Check if user has purchased this product
    const order = await prisma.order.findFirst({
        where: {
            userId,
            status: { in: ['PAID', 'DELIVERED'] },
            items: {
                some: {
                    productId
                }
            }
        },
        include: {
            items: {
                where: { productId },
                include: { product: true }
            }
        }
    });
    if (!order) {
        throw new shared_1.AppError('Product not purchased or order not paid', 403);
    }
    const product = order.items[0].product;
    // Check if product file exists
    if (!product.fileUrl || !fs_1.default.existsSync(path_1.default.join(process.env.UPLOAD_DIR || './uploads', product.fileUrl.replace('/uploads/', '')))) {
        throw new shared_1.AppError('Product file not available', 404);
    }
    // Check existing download record
    let download = await prisma.download.findFirst({
        where: {
            userId,
            productId,
            expiresAt: { gt: new Date() }
        }
    });
    if (!download) {
        // Create new download record
        const downloadToken = (0, shared_1.generateSecureToken)(32);
        const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        download = await prisma.download.create({
            data: {
                userId,
                productId,
                downloadUrl: `/api/downloads/file/${downloadToken}`,
                expiresAt
            }
        });
    }
    res.json((0, shared_1.createApiResponse)(true, {
        downloadUrl: download.downloadUrl,
        expiresAt: download.expiresAt,
        product: {
            id: product.id,
            name: product.name,
            fileName: product.fileName,
            fileSize: product.fileSize
        }
    }, 'Download link generated successfully'));
}));
// Download file with token
router.get('/file/:token', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = req.params;
    // Find download record by token
    const download = await prisma.download.findFirst({
        where: {
            downloadUrl: `/api/downloads/file/${token}`,
            expiresAt: { gt: new Date() }
        },
        include: {
            product: true,
            user: true
        }
    });
    if (!download) {
        throw new shared_1.AppError('Download link expired or invalid', 404);
    }
    const product = download.product;
    const filePath = path_1.default.join(process.env.UPLOAD_DIR || './uploads', product.fileUrl.replace('/uploads/', ''));
    // Check if file exists
    if (!fs_1.default.existsSync(filePath)) {
        throw new shared_1.AppError('File not found', 404);
    }
    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${product.fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', product.fileSize);
    // Stream file to response
    const fileStream = fs_1.default.createReadStream(filePath);
    fileStream.pipe(res);
    // Log download
    console.log(`File downloaded: ${product.name} by user ${download.user.email}`);
}));
// Get user's download history
router.get('/history', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    const downloads = await prisma.download.findMany({
        where: { userId },
        skip: offset,
        take: limitNum,
        orderBy: { createdAt: 'desc' },
        include: {
            product: {
                select: {
                    id: true,
                    name: true,
                    fileName: true,
                    fileSize: true,
                    thumbnailUrl: true
                }
            }
        }
    });
    const total = await prisma.download.count({ where: { userId } });
    res.json((0, shared_1.createApiResponse)(true, {
        downloads,
        pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            totalPages: Math.ceil(total / limitNum)
        }
    }));
}));
// Check download availability for product
router.get('/check/:productId', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId } = req.params;
    const userId = req.user.id;
    // Check if user has purchased this product
    const order = await prisma.order.findFirst({
        where: {
            userId,
            status: { in: ['PAID', 'DELIVERED'] },
            items: {
                some: { productId }
            }
        }
    });
    const isPurchased = !!order;
    // Check if download link exists and is valid
    const download = await prisma.download.findFirst({
        where: {
            userId,
            productId,
            expiresAt: { gt: new Date() }
        }
    });
    res.json((0, shared_1.createApiResponse)(true, {
        isPurchased,
        hasValidDownload: !!download,
        downloadUrl: download?.downloadUrl,
        expiresAt: download?.expiresAt
    }));
}));
exports.default = router;
//# sourceMappingURL=downloads.js.map