"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Get all wallets (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const wallets = await prisma.cryptoWallet.findMany({
        orderBy: { createdAt: 'desc' }
    });
    res.json((0, shared_1.createApiResponse)(true, { wallets }));
}));
// Get active wallets (public - for payment options)
router.get('/active', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const wallets = await prisma.cryptoWallet.findMany({
        where: { isActive: true },
        select: {
            id: true,
            currency: true,
            address: true
        },
        orderBy: { currency: 'asc' }
    });
    res.json((0, shared_1.createApiResponse)(true, { wallets }));
}));
// Create wallet (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, validation_1.validate)(validation_1.schemas.createWallet), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currency, address } = req.body;
    // Validate crypto address format
    if (!(0, shared_1.validateCryptoAddress)(address, currency)) {
        throw new shared_1.AppError(`Invalid ${currency} address format`, 400);
    }
    // Check if wallet already exists
    const existingWallet = await prisma.cryptoWallet.findUnique({
        where: { address }
    });
    if (existingWallet) {
        throw new shared_1.AppError('Wallet address already exists', 409);
    }
    const wallet = await prisma.cryptoWallet.create({
        data: {
            currency,
            address
        }
    });
    res.status(201).json((0, shared_1.createApiResponse)(true, { wallet }, 'Wallet created successfully'));
}));
// Update wallet (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { address, isActive } = req.body;
    const existingWallet = await prisma.cryptoWallet.findUnique({
        where: { id }
    });
    if (!existingWallet) {
        throw new shared_1.AppError('Wallet not found', 404);
    }
    const updateData = {};
    if (address !== undefined) {
        // Validate new address format
        if (!(0, shared_1.validateCryptoAddress)(address, existingWallet.currency)) {
            throw new shared_1.AppError(`Invalid ${existingWallet.currency} address format`, 400);
        }
        // Check if new address already exists
        const duplicateWallet = await prisma.cryptoWallet.findFirst({
            where: {
                address,
                id: { not: id }
            }
        });
        if (duplicateWallet) {
            throw new shared_1.AppError('Wallet address already exists', 409);
        }
        updateData.address = address;
    }
    if (isActive !== undefined) {
        updateData.isActive = isActive;
    }
    const wallet = await prisma.cryptoWallet.update({
        where: { id },
        data: updateData
    });
    res.json((0, shared_1.createApiResponse)(true, { wallet }, 'Wallet updated successfully'));
}));
// Delete wallet (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const existingWallet = await prisma.cryptoWallet.findUnique({
        where: { id }
    });
    if (!existingWallet) {
        throw new shared_1.AppError('Wallet not found', 404);
    }
    // Check if wallet is being used in any pending orders
    const pendingOrders = await prisma.order.count({
        where: {
            paymentAddress: existingWallet.address,
            status: 'PENDING'
        }
    });
    if (pendingOrders > 0) {
        throw new shared_1.AppError('Cannot delete wallet with pending orders', 400);
    }
    await prisma.cryptoWallet.delete({
        where: { id }
    });
    res.json((0, shared_1.createApiResponse)(true, null, 'Wallet deleted successfully'));
}));
// Get wallet by currency (public)
router.get('/currency/:currency', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currency } = req.params;
    const wallet = await prisma.cryptoWallet.findFirst({
        where: {
            currency: currency.toUpperCase(),
            isActive: true
        },
        select: {
            id: true,
            currency: true,
            address: true
        }
    });
    if (!wallet) {
        throw new shared_1.AppError(`No active wallet found for ${currency}`, 404);
    }
    res.json((0, shared_1.createApiResponse)(true, { wallet }));
}));
exports.default = router;
//# sourceMappingURL=wallets.js.map