"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Get all products (public with optional auth for admin features)
router.get('/', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 10, category, search, tags, minPrice, maxPrice, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    // Build where clause
    const where = {};
    // Only show active products for non-admin users
    if (!req.user || req.user.role !== shared_1.UserRole.ADMIN) {
        where.isActive = true;
    }
    if (category) {
        where.category = category;
    }
    if (search) {
        where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
        ];
    }
    if (tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags];
        where.tags = { hasSome: tagArray };
    }
    if (minPrice || maxPrice) {
        where.price = {};
        if (minPrice)
            where.price.gte = parseFloat(minPrice);
        if (maxPrice)
            where.price.lte = parseFloat(maxPrice);
    }
    // Get total count
    const total = await prisma.product.count({ where });
    // Get products
    const products = await prisma.product.findMany({
        where,
        skip: offset,
        take: limitNum,
        orderBy: { [sortBy]: sortOrder },
        select: {
            id: true,
            name: true,
            description: true,
            price: true,
            currency: true,
            category: true,
            tags: true,
            isActive: true,
            fileSize: true,
            downloadLimit: true,
            previewUrl: true,
            thumbnailUrl: true,
            createdAt: true,
            updatedAt: true,
            // Only include file details for admin users
            ...(req.user?.role === shared_1.UserRole.ADMIN && {
                fileUrl: true,
                fileName: true
            })
        }
    });
    const pagination = (0, shared_1.calculatePagination)(pageNum, limitNum, total);
    res.json((0, shared_1.createApiResponse)(true, {
        products,
        pagination
    }));
}));
// Get single product
router.get('/:id', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const where = { id };
    // Only show active products for non-admin users
    if (!req.user || req.user.role !== shared_1.UserRole.ADMIN) {
        where.isActive = true;
    }
    const product = await prisma.product.findFirst({
        where,
        select: {
            id: true,
            name: true,
            description: true,
            price: true,
            currency: true,
            category: true,
            tags: true,
            isActive: true,
            fileSize: true,
            downloadLimit: true,
            previewUrl: true,
            thumbnailUrl: true,
            createdAt: true,
            updatedAt: true,
            // Only include file details for admin users
            ...(req.user?.role === shared_1.UserRole.ADMIN && {
                fileUrl: true,
                fileName: true
            })
        }
    });
    if (!product) {
        throw new shared_1.AppError('Product not found', 404);
    }
    res.json((0, shared_1.createApiResponse)(true, { product }));
}));
// Create product (admin only)
router.post('/', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, validation_1.validate)(validation_1.schemas.createProduct), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const productData = req.body;
    const product = await prisma.product.create({
        data: {
            ...productData,
            fileUrl: '', // Will be updated when file is uploaded
            fileName: '',
            fileSize: 0
        }
    });
    res.status(201).json((0, shared_1.createApiResponse)(true, { product }, 'Product created successfully'));
}));
// Update product (admin only)
router.put('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, validation_1.validate)(validation_1.schemas.updateProduct), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const existingProduct = await prisma.product.findUnique({
        where: { id }
    });
    if (!existingProduct) {
        throw new shared_1.AppError('Product not found', 404);
    }
    const product = await prisma.product.update({
        where: { id },
        data: updateData
    });
    res.json((0, shared_1.createApiResponse)(true, { product }, 'Product updated successfully'));
}));
// Delete product (admin only)
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const existingProduct = await prisma.product.findUnique({
        where: { id }
    });
    if (!existingProduct) {
        throw new shared_1.AppError('Product not found', 404);
    }
    await prisma.product.delete({
        where: { id }
    });
    res.json((0, shared_1.createApiResponse)(true, null, 'Product deleted successfully'));
}));
// Get product categories
router.get('/meta/categories', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const categories = await prisma.product.findMany({
        where: { isActive: true },
        select: { category: true },
        distinct: ['category']
    });
    const categoryList = categories.map(c => c.category);
    res.json((0, shared_1.createApiResponse)(true, { categories: categoryList }));
}));
// Get product tags
router.get('/meta/tags', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const products = await prisma.product.findMany({
        where: { isActive: true },
        select: { tags: true }
    });
    const allTags = products.flatMap(p => p.tags);
    const uniqueTags = [...new Set(allTags)];
    res.json((0, shared_1.createApiResponse)(true, { tags: uniqueTags }));
}));
exports.default = router;
//# sourceMappingURL=products.js.map