{"version": 3, "file": "analytics.js", "sourceRoot": "", "sources": ["../../src/routes/analytics.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,2CAA8C;AAC9C,6CAA0E;AAC1E,6DAA0D;AAC1D,8CAA6E;AAE7E,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAChC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC,mCAAmC;AACnC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,mBAAY,EAAE,IAAA,gBAAS,EAAC,iBAAQ,CAAC,KAAK,CAAC,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IACzG,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAgB,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAE9C,2BAA2B;IAC3B,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE,cAAc,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACpE,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACrB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;YACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;SAC5B,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACjB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,EAAE;gBACL,IAAI,EAAE,iBAAQ,CAAC,QAAQ;gBACvB,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF,CAAC;KACH,CAAC,CAAC;IAEH,eAAe;IACf,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;QACjD,EAAE,EAAE,CAAC,WAAW,CAAC;QACjB,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF;QACD,IAAI,EAAE;YACJ,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,IAAI;SACZ;QACD,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,KAAK,EAAE,MAAM;aACd;SACF;QACD,IAAI,EAAE,EAAE;KACT,CAAC,CAAC;IAEH,MAAM,sBAAsB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9C,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QAC7B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;YAC7B,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;SACjC,CAAC,CAAC;QACH,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,OAAO,EAAE,IAAI,IAAI,iBAAiB;YAC/C,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;YACnC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;SAC9B,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;IAEF,iBAAiB;IACjB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS,CAAA;;;;;;;0BAOnB,SAAS;;;GAGvB,CAAC;IAEX,mBAAmB;IACnB,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;QAChD,EAAE,EAAE,CAAC,QAAQ,CAAC;QACd,KAAK,EAAE;YACL,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SAC9B;QACD,MAAM,EAAE;YACN,MAAM,EAAE,IAAI;SACb;KACF,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnD,MAAM,EAAE,IAAI,CAAC,MAAM;QACnB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;KAC1B,CAAC,CAAC,CAAC;IAEJ,MAAM,SAAS,GAAG;QAChB,YAAY,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;QAChD,WAAW;QACX,cAAc;QACd,WAAW,EAAE,sBAAsB;QACnC,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACtC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;SACnC,CAAC,CAAC;QACH,cAAc,EAAE,gBAAgB;KACjC,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC;AAEJ,qCAAqC;AACrC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,mBAAY,EAAE,IAAA,gBAAS,EAAC,iBAAQ,CAAC,KAAK,CAAC,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IAC5G,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAgB,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAE9C,sBAAsB;IACtB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QACjD,MAAM,EAAE;YACN,EAAE,EAAE,IAAI;YACR,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,IAAI;YACf,MAAM,EAAE;gBACN,MAAM,EAAE;oBACN,UAAU,EAAE;wBACV,KAAK,EAAE;4BACL,KAAK,EAAE;gCACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gCACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;6BAC9B;yBACF;qBACF;iBACF;aACF;SACF;QACD,OAAO,EAAE;YACP,UAAU,EAAE;gBACV,MAAM,EAAE,MAAM;aACf;SACF;KACF,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;QACnD,EAAE,EAAE,CAAC,WAAW,CAAC;QACjB,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF;QACD,IAAI,EAAE;YACJ,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,IAAI;SACZ;KACF,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;IAE9B,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;YAC7B,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC3B,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC;YACD,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1C,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACzC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QACxF,QAAQ;QACR,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC,CAAC,CAAC;IAEJ,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE;QAC/B,YAAY;QACZ,mBAAmB;KACpB,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC,CAAC;AAEJ,sCAAsC;AACtC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,mBAAY,EAAE,IAAA,gBAAS,EAAC,iBAAQ,CAAC,KAAK,CAAC,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IAC7G,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAgB,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAE9C,mCAAmC;IACnC,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,SAAS,CAAA;;;;;;0BAMpB,SAAS;;;GAGvB,CAAC;IAEX,4BAA4B;IAC5B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;QAC9C,EAAE,EAAE,CAAC,QAAQ,CAAC;QACd,KAAK,EAAE;YACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;YACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SAC9B;QACD,IAAI,EAAE;YACJ,WAAW,EAAE,IAAI;SAClB;QACD,MAAM,EAAE;YACN,EAAE,EAAE,IAAI;SACT;QACD,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,WAAW,EAAE,MAAM;aACpB;SACF;QACD,IAAI,EAAE,EAAE;KACT,CAAC,CAAC;IAEH,MAAM,uBAAuB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/C,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAClC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE;YAC9B,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;SAClD,CAAC,CAAC;QACH,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,SAAS;YAC/B,QAAQ,EAAE,IAAI,EAAE,QAAQ,IAAI,SAAS;YACrC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;YAC1C,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;SAC/B,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE;QAC/B,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3C,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;SACjD,CAAC,CAAC;QACH,YAAY,EAAE,uBAAuB;KACtC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC,CAAC;AAEJ,sCAAsC;AACtC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,mBAAY,EAAE,IAAA,gBAAS,EAAC,iBAAQ,CAAC,KAAK,CAAC,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAgB,EAAE,GAAG,EAAE,EAAE;IAC7G,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAE3B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACrB,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAE/B,MAAM;IACJ,gBAAgB;IAChB,YAAY,EACZ,WAAW,EACX,cAAc;IAEd,qBAAqB;IACrB,YAAY,EACZ,WAAW,EACX,cAAc;IAEd,gBAAgB;IAChB,aAAa,EACb,mBAAmB,EACnB,aAAa;IAEb,gBAAgB;IAChB,YAAY,CACb,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACpB,QAAQ;QACR,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACrB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;aAC1B;YACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;SAC5B,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACjB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;aAC1B;SACF,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,EAAE;gBACL,IAAI,EAAE,iBAAQ,CAAC,QAAQ;gBACvB,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE;aAC1B;SACF,CAAC;QAEF,aAAa;QACb,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACrB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;YACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;SAC5B,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACjB,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,oBAAW,CAAC,IAAI,EAAE,oBAAW,CAAC,SAAS,CAAC,EAAE;gBACzD,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,EAAE;gBACL,IAAI,EAAE,iBAAQ,CAAC,QAAQ;gBACvB,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;aAC9B;SACF,CAAC;QAEF,UAAU;QACV,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;QACtB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC;QACnD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,oBAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QAE9D,gBAAgB;QAChB,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC9B,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;iBACxC;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;yBACvB;qBACF;iBACF;aACF;SACF,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG;QAChB,KAAK,EAAE;YACL,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;YAC3C,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,cAAc;SAC1B;QACD,SAAS,EAAE;YACT,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC;YAC3C,MAAM,EAAE,WAAW;YACnB,SAAS,EAAE,cAAc;SAC1B;QACD,OAAO,EAAE;YACP,aAAa;YACb,cAAc,EAAE,mBAAmB;YACnC,aAAa;SACd;QACD,YAAY;KACb,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC,IAAA,0BAAiB,EAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC;AAEJ,kBAAe,MAAM,CAAC"}