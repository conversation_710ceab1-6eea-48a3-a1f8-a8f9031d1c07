"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const errorHandler_1 = require("../middleware/errorHandler");
const router = express_1.default.Router();
// Telegram webhook endpoint
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // This will be handled by the Telegram bot service
    // For now, just acknowledge the webhook
    console.log('Telegram webhook received:', req.body);
    res.status(200).json({ ok: true });
}));
exports.default = router;
//# sourceMappingURL=telegram.js.map