"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const shared_1 = require("@ecommerce/shared");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const productsDir = path_1.default.join(uploadDir, 'products');
const previewsDir = path_1.default.join(uploadDir, 'previews');
const thumbnailsDir = path_1.default.join(uploadDir, 'thumbnails');
[uploadDir, productsDir, previewsDir, thumbnailsDir].forEach(dir => {
    if (!fs_1.default.existsSync(dir)) {
        fs_1.default.mkdirSync(dir, { recursive: true });
    }
});
// Configure multer for file uploads
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        let uploadPath = productsDir;
        if (file.fieldname === 'preview') {
            uploadPath = previewsDir;
        }
        else if (file.fieldname === 'thumbnail') {
            uploadPath = thumbnailsDir;
        }
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        const uniqueName = (0, shared_1.generateUniqueFilename)(file.originalname);
        cb(null, uniqueName);
    }
});
const fileFilter = (req, file, cb) => {
    const allowedProductTypes = ['pdf', 'zip', 'rar', '7z', 'exe', 'dmg', 'pkg', 'deb', 'rpm', 'msi'];
    const allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    if (file.fieldname === 'product') {
        if ((0, shared_1.isValidFileType)(file.originalname, allowedProductTypes)) {
            cb(null, true);
        }
        else {
            cb(new Error(`Invalid file type. Allowed types: ${allowedProductTypes.join(', ')}`));
        }
    }
    else if (file.fieldname === 'preview' || file.fieldname === 'thumbnail') {
        if ((0, shared_1.isValidFileType)(file.originalname, allowedImageTypes)) {
            cb(null, true);
        }
        else {
            cb(new Error(`Invalid image type. Allowed types: ${allowedImageTypes.join(', ')}`));
        }
    }
    else {
        cb(new Error('Invalid field name'));
    }
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '104857600') // 100MB default
    }
});
// Upload product file
router.post('/product/:productId', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), upload.single('product'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId } = req.params;
    const file = req.file;
    if (!file) {
        throw new shared_1.AppError('No file uploaded', 400);
    }
    // Check if product exists
    const product = await prisma.product.findUnique({
        where: { id: productId }
    });
    if (!product) {
        // Clean up uploaded file
        fs_1.default.unlinkSync(file.path);
        throw new shared_1.AppError('Product not found', 404);
    }
    // Delete old file if exists
    if (product.fileUrl) {
        const oldFilePath = path_1.default.join(uploadDir, product.fileUrl.replace('/uploads/', ''));
        if (fs_1.default.existsSync(oldFilePath)) {
            fs_1.default.unlinkSync(oldFilePath);
        }
    }
    // Update product with file information
    const fileUrl = `/uploads/products/${file.filename}`;
    const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: {
            fileUrl,
            fileName: file.originalname,
            fileSize: file.size
        }
    });
    res.json((0, shared_1.createApiResponse)(true, {
        product: updatedProduct,
        file: {
            originalName: file.originalname,
            filename: file.filename,
            size: file.size,
            sizeFormatted: (0, shared_1.formatFileSize)(file.size),
            url: fileUrl
        }
    }, 'Product file uploaded successfully'));
}));
// Upload preview image
router.post('/preview/:productId', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), upload.single('preview'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId } = req.params;
    const file = req.file;
    if (!file) {
        throw new shared_1.AppError('No file uploaded', 400);
    }
    // Check if product exists
    const product = await prisma.product.findUnique({
        where: { id: productId }
    });
    if (!product) {
        // Clean up uploaded file
        fs_1.default.unlinkSync(file.path);
        throw new shared_1.AppError('Product not found', 404);
    }
    // Delete old preview if exists
    if (product.previewUrl) {
        const oldFilePath = path_1.default.join(uploadDir, product.previewUrl.replace('/uploads/', ''));
        if (fs_1.default.existsSync(oldFilePath)) {
            fs_1.default.unlinkSync(oldFilePath);
        }
    }
    // Update product with preview URL
    const previewUrl = `/uploads/previews/${file.filename}`;
    const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: { previewUrl }
    });
    res.json((0, shared_1.createApiResponse)(true, {
        product: updatedProduct,
        preview: {
            originalName: file.originalname,
            filename: file.filename,
            size: file.size,
            url: previewUrl
        }
    }, 'Preview image uploaded successfully'));
}));
// Upload thumbnail image
router.post('/thumbnail/:productId', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), upload.single('thumbnail'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId } = req.params;
    const file = req.file;
    if (!file) {
        throw new shared_1.AppError('No file uploaded', 400);
    }
    // Check if product exists
    const product = await prisma.product.findUnique({
        where: { id: productId }
    });
    if (!product) {
        // Clean up uploaded file
        fs_1.default.unlinkSync(file.path);
        throw new shared_1.AppError('Product not found', 404);
    }
    // Delete old thumbnail if exists
    if (product.thumbnailUrl) {
        const oldFilePath = path_1.default.join(uploadDir, product.thumbnailUrl.replace('/uploads/', ''));
        if (fs_1.default.existsSync(oldFilePath)) {
            fs_1.default.unlinkSync(oldFilePath);
        }
    }
    // Update product with thumbnail URL
    const thumbnailUrl = `/uploads/thumbnails/${file.filename}`;
    const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: { thumbnailUrl }
    });
    res.json((0, shared_1.createApiResponse)(true, {
        product: updatedProduct,
        thumbnail: {
            originalName: file.originalname,
            filename: file.filename,
            size: file.size,
            url: thumbnailUrl
        }
    }, 'Thumbnail uploaded successfully'));
}));
// Delete file
router.delete('/:productId/:fileType', auth_1.authenticate, (0, auth_1.authorize)(shared_1.UserRole.ADMIN), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { productId, fileType } = req.params;
    if (!['product', 'preview', 'thumbnail'].includes(fileType)) {
        throw new shared_1.AppError('Invalid file type', 400);
    }
    const product = await prisma.product.findUnique({
        where: { id: productId }
    });
    if (!product) {
        throw new shared_1.AppError('Product not found', 404);
    }
    let fileUrl = null;
    let updateData = {};
    switch (fileType) {
        case 'product':
            fileUrl = product.fileUrl;
            updateData = { fileUrl: '', fileName: '', fileSize: 0 };
            break;
        case 'preview':
            fileUrl = product.previewUrl;
            updateData = { previewUrl: null };
            break;
        case 'thumbnail':
            fileUrl = product.thumbnailUrl;
            updateData = { thumbnailUrl: null };
            break;
    }
    if (fileUrl) {
        const filePath = path_1.default.join(uploadDir, fileUrl.replace('/uploads/', ''));
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
        }
    }
    await prisma.product.update({
        where: { id: productId },
        data: updateData
    });
    res.json((0, shared_1.createApiResponse)(true, null, `${fileType} file deleted successfully`));
}));
exports.default = router;
//# sourceMappingURL=upload.js.map