import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
export declare const validate: (schema: Joi.ObjectSchema) => (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const schemas: {
    register: Joi.ObjectSchema<any>;
    login: Joi.ObjectSchema<any>;
    createProduct: Joi.ObjectSchema<any>;
    updateProduct: Joi.ObjectSchema<any>;
    createOrder: Joi.ObjectSchema<any>;
    createCoupon: Joi.ObjectSchema<any>;
    createWallet: Joi.ObjectSchema<any>;
    updateUser: Joi.ObjectSchema<any>;
    changePassword: Joi.ObjectSchema<any>;
};
//# sourceMappingURL=validation.d.ts.map