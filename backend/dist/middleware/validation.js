"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.schemas = exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                error: error.details[0].message
            });
        }
        next();
    };
};
exports.validate = validate;
// Common validation schemas
exports.schemas = {
    // Auth schemas
    register: joi_1.default.object({
        email: joi_1.default.string().email().required(),
        password: joi_1.default.string().min(8).required(),
        username: joi_1.default.string().min(3).max(30).optional()
    }),
    login: joi_1.default.object({
        email: joi_1.default.string().email().required(),
        password: joi_1.default.string().required()
    }),
    // Product schemas
    createProduct: joi_1.default.object({
        name: joi_1.default.string().min(1).max(255).required(),
        description: joi_1.default.string().min(1).required(),
        price: joi_1.default.number().positive().required(),
        currency: joi_1.default.string().valid('USD', 'BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').default('USD'),
        category: joi_1.default.string().min(1).max(100).required(),
        tags: joi_1.default.array().items(joi_1.default.string().max(50)).default([]),
        downloadLimit: joi_1.default.number().integer().min(1).default(3)
    }),
    updateProduct: joi_1.default.object({
        name: joi_1.default.string().min(1).max(255).optional(),
        description: joi_1.default.string().min(1).optional(),
        price: joi_1.default.number().positive().optional(),
        currency: joi_1.default.string().valid('USD', 'BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').optional(),
        category: joi_1.default.string().min(1).max(100).optional(),
        tags: joi_1.default.array().items(joi_1.default.string().max(50)).optional(),
        downloadLimit: joi_1.default.number().integer().min(1).optional(),
        isActive: joi_1.default.boolean().optional()
    }),
    // Order schemas
    createOrder: joi_1.default.object({
        items: joi_1.default.array().items(joi_1.default.object({
            productId: joi_1.default.string().required(),
            quantity: joi_1.default.number().integer().min(1).default(1)
        })).min(1).required(),
        paymentMethod: joi_1.default.string().valid('BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').required(),
        couponCode: joi_1.default.string().optional()
    }),
    // Coupon schemas
    createCoupon: joi_1.default.object({
        code: joi_1.default.string().min(3).max(20).required(),
        discountType: joi_1.default.string().valid('PERCENTAGE', 'FIXED_AMOUNT').required(),
        discountValue: joi_1.default.number().positive().required(),
        minOrderAmount: joi_1.default.number().positive().optional(),
        maxUses: joi_1.default.number().integer().positive().optional(),
        expiresAt: joi_1.default.date().greater('now').optional()
    }),
    // Wallet schemas
    createWallet: joi_1.default.object({
        currency: joi_1.default.string().valid('BITCOIN', 'ETHEREUM', 'USDT', 'LITECOIN').required(),
        address: joi_1.default.string().min(10).required()
    }),
    // User schemas
    updateUser: joi_1.default.object({
        email: joi_1.default.string().email().optional(),
        username: joi_1.default.string().min(3).max(30).optional(),
        role: joi_1.default.string().valid('ADMIN', 'CUSTOMER').optional(),
        isActive: joi_1.default.boolean().optional()
    }),
    changePassword: joi_1.default.object({
        currentPassword: joi_1.default.string().required(),
        newPassword: joi_1.default.string().min(8).required()
    })
};
//# sourceMappingURL=validation.js.map