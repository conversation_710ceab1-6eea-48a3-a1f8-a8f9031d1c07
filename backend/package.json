{"name": "@ecommerce/backend", "version": "1.0.0", "description": "Backend API for the e-commerce system", "main": "dist/app.js", "scripts": {"dev": "nodemon --exec ts-node src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@ecommerce/shared": "file:../shared", "@prisma/client": "^5.7.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "nodemon": "^3.0.2", "prisma": "^5.7.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}