version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ecommerce_postgres
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ecommerce_network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: ecommerce_redis
    ports:
      - "6379:6379"
    networks:
      - ecommerce_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ecommerce_backend
    environment:
      DATABASE_URL: ***********************************************/ecommerce_db
      JWT_SECRET: your-super-secret-jwt-key-here
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-here
      NODE_ENV: development
      PORT: 4000
      CORS_ORIGIN: http://localhost:4002
    ports:
      - "4000:4000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - ecommerce_network
    command: npm run dev

  # Admin Panel
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: ecommerce_admin
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:4000/api
    ports:
      - "4002:4002"
    volumes:
      - ./admin-panel:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - ecommerce_network
    command: npm run dev

  # Telegram Bot
  telegram-bot:
    build:
      context: ./telegram-bot
      dockerfile: Dockerfile
    container_name: ecommerce_telegram_bot
    environment:
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      API_BASE_URL: http://backend:4000/api
      NODE_ENV: development
    volumes:
      - ./telegram-bot:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ecommerce_network
    command: npm run dev

volumes:
  postgres_data:
  uploads_data:

networks:
  ecommerce_network:
    driver: bridge
