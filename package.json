{"name": "digital-ecommerce-system", "version": "1.0.0", "description": "Complete e-commerce system for digital products with web admin and Telegram bot", "private": true, "workspaces": ["backend", "admin-panel", "telegram-bot", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:admin\" \"npm run dev:bot\"", "dev:backend": "cd backend && npm run dev", "dev:admin": "cd admin-panel && npm run dev", "dev:bot": "cd telegram-bot && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:admin && npm run build:bot", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:admin": "cd admin-panel && npm run build", "build:bot": "cd telegram-bot && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:admin\" \"npm run start:bot\"", "start:backend": "cd backend && npm start", "start:admin": "cd admin-panel && npm start", "start:bot": "cd telegram-bot && npm start", "db:generate": "cd backend && npx prisma generate", "db:migrate": "cd backend && npx prisma migrate dev", "db:studio": "cd backend && npx prisma studio"}, "devDependencies": {"concurrently": "^8.2.2"}}