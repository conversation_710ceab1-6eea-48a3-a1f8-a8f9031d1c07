# Digital E-commerce System

A complete e-commerce platform for selling digital products with both web admin panel and Telegram bot interfaces. Built with modern technologies and cryptocurrency payment support.

## 🚀 Features

### Web Admin Panel
- **Dashboard** with real-time analytics and key metrics
- **Product Management** - Create, edit, delete digital products
- **File Upload System** - Support for product files, thumbnails, and previews
- **Order Management** - Track and manage customer orders
- **User Management** - Manage customers and admin accounts
- **Coupon System** - Create and manage discount codes
- **Cryptocurrency Wallets** - Configure payment addresses
- **Analytics & Reporting** - Sales analytics and performance metrics
- **Role-based Access Control** - Admin and customer roles

### Telegram Bot
- **Product Catalog** - Browse products with search and filters
- **Shopping Cart** - Add/remove products, manage quantities
- **Secure Checkout** - Cryptocurrency payment processing
- **User Authentication** - Register and login system
- **Order Tracking** - View order history and status
- **File Downloads** - Secure download links for purchased products
- **Payment Verification** - Automatic payment confirmation
- **Customer Support** - Integrated help system

### Backend API
- **RESTful API** - Complete API for all operations
- **Authentication** - JWT-based auth with refresh tokens
- **File Management** - Secure file upload and download system
- **Payment Processing** - Cryptocurrency payment integration
- **Database** - PostgreSQL with Prisma ORM
- **Security** - Rate limiting, CORS, helmet protection
- **Validation** - Input validation and sanitization

## 🛠 Technology Stack

- **Backend**: Node.js, Express.js, TypeScript, Prisma ORM
- **Database**: PostgreSQL
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Bot**: Telegraf (Telegram Bot Framework)
- **Authentication**: JWT tokens
- **File Storage**: Local storage (extensible to cloud)
- **Payments**: Cryptocurrency (Bitcoin, Ethereum, USDT, Litecoin)
- **Deployment**: Docker, Docker Compose

## 📋 Prerequisites

- Node.js 18+ and npm
- PostgreSQL 15+
- Docker and Docker Compose (for containerized setup)
- Telegram Bot Token (from @BotFather)

## 🚀 Quick Start

### Option 1: Docker Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd digital-ecommerce-system
   ```

2. **Set up environment variables**
   ```bash
   cp backend/.env.example backend/.env
   cp admin-panel/.env.local.example admin-panel/.env.local
   cp telegram-bot/.env.example telegram-bot/.env
   ```

3. **Configure your Telegram Bot**
   - Create a bot with @BotFather on Telegram
   - Add your bot token to `telegram-bot/.env`

4. **Start the application**
   ```bash
   docker-compose up -d
   ```

5. **Initialize the database**
   ```bash
   docker-compose exec backend npx prisma migrate dev
   docker-compose exec backend npx prisma db seed
   ```

6. **Access the applications**
   - Admin Panel: http://localhost:3000
   - API: http://localhost:3001
   - Telegram Bot: Search for your bot on Telegram

### Option 2: Manual Setup

1. **Install dependencies**
   ```bash
   npm install
   npm run build:shared
   ```

2. **Set up PostgreSQL database**
   ```bash
   createdb ecommerce_db
   ```

3. **Configure environment variables**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database URL and other settings
   ```

4. **Run database migrations**
   ```bash
   cd backend
   npx prisma migrate dev
   npx prisma db seed
   ```

5. **Start the services**
   ```bash
   # Terminal 1 - Backend
   cd backend && npm run dev

   # Terminal 2 - Admin Panel
   cd admin-panel && npm run dev

   # Terminal 3 - Telegram Bot
   cd telegram-bot && npm run dev
   ```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_db"
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"
PORT=3001
UPLOAD_DIR="./uploads"
CORS_ORIGIN="http://localhost:3000"
```

#### Telegram Bot (.env)
```env
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
API_BASE_URL="http://localhost:3001/api"
```

#### Admin Panel (.env.local)
```env
NEXT_PUBLIC_API_URL="http://localhost:3001/api"
```

### Cryptocurrency Wallets

Configure your cryptocurrency wallet addresses in the admin panel:
1. Login to admin panel
2. Go to Wallets section
3. Add wallet addresses for each supported currency
4. Activate the wallets you want to use

## 📱 Using the Telegram Bot

1. **Start the bot**: Send `/start` to your bot
2. **Register**: Use `/register email password username`
3. **Login**: Use `/login email password`
4. **Browse products**: Use the Products menu
5. **Add to cart**: Select products and add to cart
6. **Checkout**: Choose payment method and complete purchase
7. **Download**: Access your purchased files through the bot

## 🔐 Default Login Credentials

After running the seed script:

- **Admin Panel**: <EMAIL> / admin123
- **Test Customer**: <EMAIL> / customer123

## 📊 API Documentation

The API provides the following endpoints:

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh tokens
- `GET /api/auth/me` - Get current user

### Products
- `GET /api/products` - List products
- `POST /api/products` - Create product (admin)
- `PUT /api/products/:id` - Update product (admin)
- `DELETE /api/products/:id` - Delete product (admin)

### Orders
- `GET /api/orders` - List orders
- `POST /api/orders` - Create order
- `PUT /api/orders/:id/status` - Update order status (admin)

### File Management
- `POST /api/upload/product/:id` - Upload product file
- `POST /api/upload/thumbnail/:id` - Upload thumbnail
- `GET /api/downloads/generate/:productId` - Generate download link
- `GET /api/downloads/file/:token` - Download file

## 🔒 Security Features

- JWT authentication with refresh tokens
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS protection
- Helmet security headers
- Secure file upload with type validation
- Download link expiration
- Role-based access control

## 🚀 Deployment

### Production Deployment

1. **Set up production environment**
   ```bash
   cp docker-compose.prod.yml docker-compose.yml
   ```

2. **Configure production environment variables**
   - Set strong JWT secrets
   - Configure production database
   - Set up SSL certificates
   - Configure domain names

3. **Deploy with Docker**
   ```bash
   docker-compose up -d --build
   ```

### Environment-specific Configurations

- **Development**: Hot reload, debug logging
- **Production**: Optimized builds, error logging, SSL

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact: <EMAIL>

## 🔄 Updates and Maintenance

- Regular security updates
- Database backups
- Monitor system performance
- Update dependencies
- Review and rotate secrets

---

**Built with ❤️ for the digital economy**
