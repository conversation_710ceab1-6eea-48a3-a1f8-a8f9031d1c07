"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const stream = __importStar(require("stream"));
const check_1 = require("../helpers/check");
const sandwich_stream_1 = __importDefault(require("sandwich-stream"));
const CRNL = '\r\n';
class MultipartStream extends sandwich_stream_1.default {
    constructor(boundary) {
        super({
            head: `--${boundary}${CRNL}`,
            tail: `${CRNL}--${boundary}--`,
            separator: `${CRNL}--${boundary}${CRNL}`,
        });
    }
    addPart(part) {
        const partStream = new stream.PassThrough();
        for (const [key, header] of Object.entries(part.headers)) {
            partStream.write(`${key}:${header}${CRNL}`);
        }
        partStream.write(CRNL);
        if (MultipartStream.isStream(part.body)) {
            part.body.pipe(partStream);
        }
        else {
            partStream.end(part.body);
        }
        this.add(partStream);
    }
    static isStream(stream) {
        return (typeof stream === 'object' &&
            stream !== null &&
            (0, check_1.hasPropType)(stream, 'pipe', 'function'));
    }
}
exports.default = MultipartStream;
