{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../src/core/network/client.ts"], "names": [], "mappings": ";;AAIA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAA;AAK5B,OAAO,EAAa,IAAI,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAI9C,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAA;AAazB,kBAAU,SAAS,CAAC;IAClB,KAAY,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;IAC7E,UAAiB,OAAO;QACtB;;WAEG;QACH,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAA;QAClB;;;;;WAKG;QACH,eAAe,CAAC,EAAE,KAAK,CAAA;QACvB,OAAO,EAAE,MAAM,CAAA;QACf;;;WAGG;QACH,OAAO,EAAE,KAAK,GAAG,MAAM,CAAA;QACvB,YAAY,EAAE,OAAO,CAAA;QACrB,OAAO,EAAE,OAAO,CAAA;KACjB;IAED,UAAiB,cAAc;QAC7B,MAAM,CAAC,EAAE,WAAW,CAAA;KACrB;CACF;AAuPD,KAAK,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAA;AACnC,cAAM,SAAS;IAIX,QAAQ,CAAC,KAAK,EAAE,MAAM;IAEtB,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAL5B,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,CAAA;gBAGxB,KAAK,EAAE,MAAM,EACtB,OAAO,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EACnB,QAAQ,CAAC,sBAAU;IAWtC;;;;;;;;;OASG;IACH,IAAI,YAAY,CAAC,MAAM,EAAE,OAAO,EAE/B;IAED,IAAI,YAAY,IAJS,OAAO,CAM/B;IAEK,OAAO,CAAC,CAAC,SAAS,MAAM,QAAQ,EACpC,MAAM,EAAE,CAAC,EACT,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAChB,EAAE,MAAM,EAAE,GAAE,SAAS,CAAC,cAAmB,GACxC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;CAoDpC;AAED,eAAe,SAAS,CAAA"}