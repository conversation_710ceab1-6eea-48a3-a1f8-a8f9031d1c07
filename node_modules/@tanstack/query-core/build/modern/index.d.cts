export { P as AnyDataTag, b4 as CancelOptions, C as CancelledError, T as DataTag, F as DefaultError, b3 as DefaultOptions, ai as DefaultedInfiniteQueryObserverOptions, ag as DefaultedQueryObserverOptions, aN as DefinedInfiniteQueryObserverResult, aE as DefinedQueryObserverResult, D as DehydrateOptions, z as DehydratedState, A as DistributiveOmit, Z as Enabled, al as EnsureInfiniteQueryDataOptions, ak as EnsureQueryDataOptions, am as FetchInfiniteQueryOptions, at as FetchNextPageOptions, au as FetchPreviousPageOptions, aj as FetchQueryOptions, aw as FetchStatus, a5 as GetNextPageParamFunction, a4 as GetPreviousPageParamFunction, H as HydrateOptions, V as InferDataFromTag, W as InferErrorFromTag, a6 as InfiniteData, aG as InfiniteQueryObserverBaseResult, aJ as InfiniteQueryObserverLoadingErrorResult, aI as InfiniteQueryObserverLoadingResult, ah as InfiniteQueryObserverOptions, aH as InfiniteQueryObserverPendingResult, aM as InfiniteQueryObserverPlaceholderResult, aK as InfiniteQueryObserverRefetchErrorResult, aO as InfiniteQueryObserverResult, aL as InfiniteQueryObserverSuccessResult, ac as InfiniteQueryPageParamsOptions, a0 as InitialDataFunction, ab as InitialPageParam, ar as InvalidateOptions, ap as InvalidateQueryFilters, aX as MutateFunction, aW as MutateOptions, y as Mutation, M as MutationCache, d as MutationCacheNotifyEvent, j as MutationFilters, aT as MutationFunction, aP as MutationKey, aS as MutationMeta, e as MutationObserver, aY as MutationObserverBaseResult, a$ as MutationObserverErrorResult, aZ as MutationObserverIdleResult, a_ as MutationObserverLoadingResult, aV as MutationObserverOptions, b1 as MutationObserverResult, b0 as MutationObserverSuccessResult, aU as MutationOptions, aR as MutationScope, x as MutationState, aQ as MutationStatus, a8 as NetworkMode, E as NoInfer, N as NonUndefinedGuard, b7 as NotifyEvent, b6 as NotifyEventType, a9 as NotifyOnChangeProps, O as OmitKeyof, B as Override, a1 as PlaceholderDataFunction, a2 as QueriesPlaceholderDataFunction, w as Query, Q as QueryCache, a as QueryCacheNotifyEvent, b as QueryClient, b2 as QueryClientConfig, l as QueryFilters, X as QueryFunction, $ as QueryFunctionContext, G as QueryKey, a3 as QueryKeyHashFunction, a7 as QueryMeta, c as QueryObserver, ax as QueryObserverBaseResult, aA as QueryObserverLoadingErrorResult, az as QueryObserverLoadingResult, ae as QueryObserverOptions, ay as QueryObserverPendingResult, aD as QueryObserverPlaceholderResult, aB as QueryObserverRefetchErrorResult, aF as QueryObserverResult, aC as QueryObserverSuccessResult, aa as QueryOptions, _ as QueryPersister, v as QueryState, av as QueryStatus, ao as RefetchOptions, aq as RefetchQueryFilters, R as Register, as as ResetOptions, an as ResultOptions, b5 as SetDataOptions, S as SkipToken, Y as StaleTime, ad as ThrowOnError, L as UnsetMarker, U as Updater, af as WithRequired, J as dataTagErrorSymbol, I as dataTagSymbol, u as defaultShouldDehydrateMutation, t as defaultShouldDehydrateQuery, p as dehydrate, h as hashKey, q as hydrate, o as isCancelledError, i as isServer, k as keepPreviousData, f as matchMutation, m as matchQuery, n as noop, r as replaceEqualDeep, g as shouldThrowError, s as skipToken, K as unsetMarker } from './hydration-TwHKRJLE.cjs';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.cjs';
export { InfiniteQueryObserver } from './infiniteQueryObserver.cjs';
export { defaultScheduler, notifyManager } from './notifyManager.cjs';
export { focusManager } from './focusManager.cjs';
export { onlineManager } from './onlineManager.cjs';
export { streamedQuery as experimental_streamedQuery } from './streamedQuery.cjs';
import './removable.cjs';
import './subscribable.cjs';
