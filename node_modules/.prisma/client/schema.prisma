// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(cuid())
  email      String   @unique
  username   String?
  telegramId String?  @unique
  password   String?
  role       UserRole @default(CUSTOMER)
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  orders    Order[]
  downloads Download[]

  @@map("users")
}

model Product {
  id            String   @id @default(cuid())
  name          String
  description   String
  price         Float
  currency      String   @default("USD")
  category      String
  tags          String[]
  isActive      Boolean  @default(true)
  fileUrl       String
  fileName      String
  fileSize      Int
  downloadLimit Int      @default(3)
  previewUrl    String?
  thumbnailUrl  String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  orderItems OrderItem[]
  downloads  Download[]

  @@map("products")
}

model Order {
  id              String        @id @default(cuid())
  userId          String
  totalAmount     Float
  currency        String        @default("USD")
  status          OrderStatus   @default(PENDING)
  paymentMethod   PaymentMethod
  paymentAddress  String?
  transactionHash String?
  couponCode      String?
  discountAmount  Float         @default(0)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

model OrderItem {
  id        String @id @default(cuid())
  orderId   String
  productId String
  quantity  Int    @default(1)
  price     Float

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Coupon {
  id             String       @id @default(cuid())
  code           String       @unique
  discountType   DiscountType
  discountValue  Float
  minOrderAmount Float?
  maxUses        Int?
  usedCount      Int          @default(0)
  isActive       Boolean      @default(true)
  expiresAt      DateTime?
  createdAt      DateTime     @default(now())

  @@map("coupons")
}

model CryptoWallet {
  id        String        @id @default(cuid())
  currency  PaymentMethod
  address   String        @unique
  isActive  Boolean       @default(true)
  createdAt DateTime      @default(now())

  @@map("crypto_wallets")
}

model Download {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  downloadUrl String
  expiresAt   DateTime
  createdAt   DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@map("downloads")
}

model SystemSettings {
  id    String @id @default(cuid())
  key   String @unique
  value String

  @@map("system_settings")
}

enum UserRole {
  ADMIN
  CUSTOMER
}

enum OrderStatus {
  PENDING
  PAID
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentMethod {
  BITCOIN
  ETHEREUM
  USDT
  LITECOIN
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}
