{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/parse-component-stack.ts"], "names": ["parseComponentStack", "componentStack", "componentStackFrames", "line", "trim", "split", "match", "exec", "component", "webpackFile", "includes", "modulePath", "replace", "file", "lineNumber", "column", "push", "Number", "undefined"], "mappings": ";;;;+BAOg<PERSON>;;;eAAAA;;;AAAT,SAASA,oBACdC,cAAsB;IAEtB,MAAMC,uBAA8C,EAAE;IAEtD,KAAK,MAAMC,QAAQF,eAAeG,IAAI,GAAGC,KAAK,CAAC,MAAO;QACpD,uDAAuD;QACvD,MAAMC,QAAQ,yBAAyBC,IAAI,CAACJ;QAC5C,IAAIG,yBAAAA,KAAO,CAAC,EAAE,EAAE;YACd,MAAME,YAAYF,KAAK,CAAC,EAAE;YAC1B,MAAMG,cAAcH,KAAK,CAAC,EAAE;YAE5B,mEAAmE;YACnE,IAAIG,+BAAAA,YAAaC,QAAQ,CAAC,cAAc;gBACtC;YACF;YAEA,MAAMC,aAAaF,+BAAAA,YAAaG,OAAO,CACrC,mDACA;gBAEiCD;YAAnC,MAAM,CAACE,MAAMC,YAAYC,OAAO,GAAGJ,CAAAA,oBAAAA,8BAAAA,WAAYN,KAAK,CAAC,KAAK,cAAvBM,oBAA6B,EAAE;YAElET,qBAAqBc,IAAI,CAAC;gBACxBR;gBACAK;gBACAC,YAAYA,aAAaG,OAAOH,cAAcI;gBAC9CH,QAAQA,SAASE,OAAOF,UAAUG;YACpC;QACF;IACF;IAEA,OAAOhB;AACT"}