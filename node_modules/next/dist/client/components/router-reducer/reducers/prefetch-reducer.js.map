{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts"], "names": ["prefetchQueue", "prefetchReducer", "PromiseQueue", "state", "action", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "href", "createHrefFromUrl", "cacheEntry", "get", "kind", "PrefetchKind", "TEMPORARY", "set", "AUTO", "FULL", "serverResponse", "enqueue", "fetchServerResponse", "tree", "nextUrl", "buildId", "treeAtTimeOfPrefetch", "data", "prefetchTime", "Date", "now", "lastUsedTime"], "mappings": ";;;;;;;;;;;;;;;IAYaA,aAAa;eAAbA;;IAEGC,eAAe;eAAfA;;;mCAdkB;qCACE;oCAMP;oCACM;kCACE;8BACR;AAEtB,MAAMD,gBAAgB,IAAIE,0BAAY,CAAC;AAEvC,SAASD,gBACdE,KAA2B,EAC3BC,MAAsB;IAEtB,4DAA4D;IAC5DC,IAAAA,sCAAkB,EAACF,MAAMG,aAAa;IAEtC,MAAM,EAAEC,GAAG,EAAE,GAAGH;IAChBG,IAAIC,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAE5C,MAAMC,OAAOC,IAAAA,oCAAiB,EAC5BL,KACA,0FAA0F;IAC1F;IAGF,MAAMM,aAAaV,MAAMG,aAAa,CAACQ,GAAG,CAACH;IAC3C,IAAIE,YAAY;QACd;;;KAGC,GACD,IAAIA,WAAWE,IAAI,KAAKC,gCAAY,CAACC,SAAS,EAAE;YAC9Cd,MAAMG,aAAa,CAACY,GAAG,CAACP,MAAM;gBAC5B,GAAGE,UAAU;gBACbE,MAAMX,OAAOW,IAAI;YACnB;QACF;QAEA;;;MAGE,GACF,IACE,CACEF,CAAAA,WAAWE,IAAI,KAAKC,gCAAY,CAACG,IAAI,IACrCf,OAAOW,IAAI,KAAKC,gCAAY,CAACI,IAAI,AAAD,GAElC;YACA,OAAOjB;QACT;IACF;IAEA,uGAAuG;IACvG,MAAMkB,iBAAiBrB,cAAcsB,OAAO,CAAC,IAC3CC,IAAAA,wCAAmB,EACjBhB,KACA,mKAAmK;QACnKJ,MAAMqB,IAAI,EACVrB,MAAMsB,OAAO,EACbtB,MAAMuB,OAAO,EACbtB,OAAOW,IAAI;IAIf,wEAAwE;IACxEZ,MAAMG,aAAa,CAACY,GAAG,CAACP,MAAM;QAC5B,wEAAwE;QACxEgB,sBAAsBxB,MAAMqB,IAAI;QAChCI,MAAMP;QACNN,MAAMX,OAAOW,IAAI;QACjBc,cAAcC,KAAKC,GAAG;QACtBC,cAAc;IAChB;IAEA,OAAO7B;AACT"}