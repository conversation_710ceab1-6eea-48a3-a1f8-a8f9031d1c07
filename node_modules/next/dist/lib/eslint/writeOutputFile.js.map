{"version": 3, "sources": ["../../../src/lib/eslint/writeOutputFile.ts"], "names": ["writeOutputFile", "isDirectory", "filePath", "fs", "stat", "then", "catch", "error", "isError", "code", "outputFile", "outputData", "path", "resolve", "process", "cwd", "Log", "mkdir", "dirname", "recursive", "writeFile", "info", "err", "console"], "mappings": ";;;;+BA6BsBA;;;eAAAA;;;oBA7BS;6DACd;6DACI;gEACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB;;;CAGC,GACD,SAASC,YACP,kCAAkC,GAClCC,QAAgB;IAEhB,OAAOC,YAAE,CACNC,IAAI,CAACF,UACLG,IAAI,CAAC,CAACD,OAASA,KAAKH,WAAW,IAC/BK,KAAK,CAAC,CAACC;QACN,IACEC,IAAAA,gBAAO,EAACD,UACPA,CAAAA,MAAME,IAAI,KAAK,YAAYF,MAAME,IAAI,KAAK,SAAQ,GACnD;YACA,OAAO;QACT;QACA,MAAMF;IACR;AACJ;AAIO,eAAeP,gBACpB,2CAA2C,GAC3CU,UAAkB,EAClB,qDAAqD,GACrDC,UAAkB;IAElB,MAAMT,WAAWU,aAAI,CAACC,OAAO,CAACC,QAAQC,GAAG,IAAIL;IAE7C,IAAI,MAAMT,YAAYC,WAAW;QAC/Bc,KAAIT,KAAK,CACP,CAAC,qDAAqD,EAAEL,SAAS,CAAC;IAEtE,OAAO;QACL,IAAI;YACF,MAAMC,YAAE,CAACc,KAAK,CAACL,aAAI,CAACM,OAAO,CAAChB,WAAW;gBAAEiB,WAAW;YAAK;YACzD,MAAMhB,YAAE,CAACiB,SAAS,CAAClB,UAAUS;YAC7BK,KAAIK,IAAI,CAAC,CAAC,kCAAkC,EAAEnB,SAAS,CAAC;QAC1D,EAAE,OAAOoB,KAAK;YACZN,KAAIT,KAAK,CAAC,CAAC,6CAA6C,EAAEL,SAAS,CAAC;YACpEqB,QAAQhB,KAAK,CAACe;QAChB;IACF;AACF"}