{"version": 3, "sources": ["../../../src/client/tracing/tracer.ts"], "names": ["mitt", "Span", "end", "endTime", "state", "Error", "Date", "now", "onSpanEnd", "constructor", "name", "options", "attributes", "startTime", "Tracer", "startSpan", "handleSpanEnd", "cb", "_emitter", "on", "off", "span", "emit"], "mappings": "AAAA,OAAOA,UAAU,wBAAuB;AAyBxC,MAAMC;IAmBJC,IAAIC,OAAgB,EAAE;QACpB,IAAI,IAAI,CAACC,KAAK,CAACA,KAAK,KAAK,SAAS;YAChC,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI,CAACD,KAAK,GAAG;YACXA,OAAO;YACPD,SAASA,kBAAAA,UAAWG,KAAKC,GAAG;QAC9B;QACA,IAAI,CAACC,SAAS,CAAC,IAAI;IACrB;IAtBAC,YACEC,IAAY,EACZC,OAAoB,EACpBH,SAA+B,CAC/B;QACA,IAAI,CAACE,IAAI,GAAGA;YACMC;QAAlB,IAAI,CAACC,UAAU,GAAGD,CAAAA,sBAAAA,QAAQC,UAAU,YAAlBD,sBAAsB,CAAC;YACxBA;QAAjB,IAAI,CAACE,SAAS,GAAGF,CAAAA,qBAAAA,QAAQE,SAAS,YAAjBF,qBAAqBL,KAAKC,GAAG;QAC9C,IAAI,CAACC,SAAS,GAAGA;QACjB,IAAI,CAACJ,KAAK,GAAG;YAAEA,OAAO;QAAa;IACrC;AAaF;AAEA,MAAMU;IAOJC,UAAUL,IAAY,EAAEC,OAAoB,EAAE;QAC5C,OAAO,IAAIV,KAAKS,MAAMC,SAAS,IAAI,CAACK,aAAa;IACnD;IAEAR,UAAUS,EAAyB,EAAc;QAC/C,IAAI,CAACC,QAAQ,CAACC,EAAE,CAAC,WAAWF;QAC5B,OAAO;YACL,IAAI,CAACC,QAAQ,CAACE,GAAG,CAAC,WAAWH;QAC/B;IACF;;aAfAC,WAAgClB;aAExBgB,gBAAgB,CAACK;YACvB,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,WAAWD;QAChC;;AAYF;AAGA,eAAe,IAAIP,SAAQ"}