{"version": 3, "sources": ["../../../src/build/templates/pages-api.ts"], "names": ["config", "routeModule", "hoist", "userland", "PagesAPIRouteModule", "definition", "kind", "RouteKind", "PAGES_API", "page", "pathname", "bundlePath", "filename"], "mappings": ";;;;;;;;;;;;;;;;IAQA,wDAAwD;IACxD,OAAyC;eAAzC;;IAGaA,MAAM;eAANA;;IAGAC,WAAW;eAAXA;;;gCAfuB;2BACV;yBAEJ;sEAGI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAG1B,WAAeC,IAAAA,cAAK,EAACC,eAAU;AAGxB,MAAMH,SAASE,IAAAA,cAAK,EAACC,eAAU;AAG/B,MAAMF,cAAc,IAAIG,mCAAmB,CAAC;IACjDC,YAAY;QACVC,MAAMC,oBAAS,CAACC,SAAS;QACzBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAT,UAAAA;AACF"}