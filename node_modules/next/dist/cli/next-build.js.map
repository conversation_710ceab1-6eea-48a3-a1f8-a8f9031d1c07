{"version": 3, "sources": ["../../src/cli/next-build.ts"], "names": ["nextBuild", "args", "printAndExit", "Log", "warn", "dir", "getProjectDir", "_", "existsSync", "process", "env", "TURBOPACK", "build", "NEXT_DEBUG_BUILD", "catch", "err", "console", "error", "isError", "code", "message"], "mappings": ";;;;;+BAsFSA;;;eAAAA;;;QApFF;oBACoB;6DACN;8DAEH;uBACW;gEACT;+BACU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,MAAMA,YAAwB,CAACC;IAC7B,IAAIA,IAAI,CAAC,SAAS,EAAE;QAClBC,IAAAA,mBAAY,EACV,CAAC;;;;;;;;;;;;;;;;;IAiBH,CAAC,EACC;IAEJ;IACA,IAAID,IAAI,CAAC,YAAY,EAAE;QACrBE,KAAIC,IAAI,CAAC;IACX;IACA,IAAIH,IAAI,CAAC,YAAY,EAAE;QACrBE,KAAIC,IAAI,CAAC;IACX;IACA,IAAIH,IAAI,CAAC,gBAAgB,EAAE;QACzBE,KAAIC,IAAI,CACN;IAEJ;IACA,MAAMC,MAAMC,IAAAA,4BAAa,EAACL,KAAKM,CAAC,CAAC,EAAE;IAEnC,yCAAyC;IACzC,IAAI,CAACC,IAAAA,cAAU,EAACH,MAAM;QACpBH,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEG,IAAI,CAAC;IACvE;IAEA,IAAIJ,IAAI,CAAC,uBAAuB,EAAE;QAChCQ,QAAQC,GAAG,CAACC,SAAS,GAAG;IAC1B;IAEA,OAAOC,IAAAA,cAAK,EACVP,KACAJ,IAAI,CAAC,YAAY,EACjBA,IAAI,CAAC,UAAU,IAAIQ,QAAQC,GAAG,CAACG,gBAAgB,EAC/C,CAACZ,IAAI,CAAC,YAAY,EAClBA,IAAI,CAAC,gBAAgB,EACrBA,IAAI,CAAC,0BAA0B,EAC/B,CAAC,CAACQ,QAAQC,GAAG,CAACC,SAAS,EACvBV,IAAI,CAAC,4BAA4B,EACjCA,IAAI,CAAC,eAAe,IAAI,WACxBa,KAAK,CAAC,CAACC;QACPC,QAAQC,KAAK,CAAC;QACd,IACEC,IAAAA,gBAAO,EAACH,QACPA,CAAAA,IAAII,IAAI,KAAK,2BACZJ,IAAII,IAAI,KAAK,oBACbJ,IAAII,IAAI,KAAK,+BACbJ,IAAII,IAAI,KAAK,uBACbJ,IAAII,IAAI,KAAK,6BACbJ,IAAII,IAAI,KAAK,8BAA6B,GAC5C;YACAjB,IAAAA,mBAAY,EAAC,CAAC,EAAE,EAAEa,IAAIK,OAAO,CAAC,CAAC;QACjC,OAAO;YACLJ,QAAQC,KAAK,CAAC;YACdf,IAAAA,mBAAY,EAACa;QACf;IACF;AACF"}