{"version": 3, "sources": ["../../../src/server/async-storage/static-generation-async-storage-wrapper.ts"], "names": ["StaticGenerationAsyncStorageWrapper", "wrap", "storage", "urlPathname", "renderOpts", "postpone", "callback", "isStaticGeneration", "supportsDynamicHTML", "isDraftMode", "isServerAction", "store", "pagePath", "originalPathname", "incrementalCache", "globalThis", "__incrementalCache", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "experimental", "ppr", "reason", "postponeWasTriggered", "undefined", "run"], "mappings": ";;;;+BAkCaA;;;eAAAA;;;AAAN,MAAMA,sCAGT;IACFC,MACEC,OAAiD,EACjD,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAA2B,EAC9DC,QAAkD;QAElD;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMC,qBACJ,CAACH,WAAWI,mBAAmB,IAC/B,CAACJ,WAAWK,WAAW,IACvB,CAACL,WAAWM,cAAc;QAE5B,MAAMC,QAA+B;YACnCJ;YACAJ;YACAS,UAAUR,WAAWS,gBAAgB;YACrCC,kBACE,qEAAqE;YACrE,mDAAmD;YACnDV,WAAWU,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;YACvEC,cAAcb,WAAWa,YAAY;YACrCC,gBAAgBd,WAAWe,UAAU;YACrCC,YAAYhB,WAAWgB,UAAU;YACjCC,sBAAsBjB,WAAWiB,oBAAoB;YAErDZ,aAAaL,WAAWK,WAAW;YAEnCJ,UACE,0EAA0E;YAC1E,6BAA6B;YAC7BE,sBAAsBH,WAAWkB,YAAY,CAACC,GAAG,IAAIlB,WACjD,CAACmB;gBACC,qDAAqD;gBACrDb,MAAMc,oBAAoB,GAAG;gBAE7B,OAAOpB,SACL,CAAC,0EAA0E,EAAEmB,OAAO,EAAE,CAAC,GACrF,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;YAEzF,IACAE;QACR;QAEA,sFAAsF;QACtFtB,WAAWO,KAAK,GAAGA;QAEnB,OAAOT,QAAQyB,GAAG,CAAChB,OAAOL,UAAUK;IACtC;AACF"}