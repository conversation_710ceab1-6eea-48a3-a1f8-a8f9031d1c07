{"c": ["webpack"], "r": ["pages/index"], "m": ["../node_modules/lucide-react/dist/esm/icons/arrow-down.js", "../node_modules/lucide-react/dist/esm/icons/arrow-up.js", "../node_modules/lucide-react/dist/esm/icons/bar-chart-3.js", "../node_modules/lucide-react/dist/esm/icons/bell.js", "../node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "../node_modules/lucide-react/dist/esm/icons/layout-dashboard.js", "../node_modules/lucide-react/dist/esm/icons/life-buoy.js", "../node_modules/lucide-react/dist/esm/icons/log-out.js", "../node_modules/lucide-react/dist/esm/icons/menu.js", "../node_modules/lucide-react/dist/esm/icons/moon.js", "../node_modules/lucide-react/dist/esm/icons/package.js", "../node_modules/lucide-react/dist/esm/icons/search.js", "../node_modules/lucide-react/dist/esm/icons/settings.js", "../node_modules/lucide-react/dist/esm/icons/shopping-bag.js", "../node_modules/lucide-react/dist/esm/icons/shopping-cart.js", "../node_modules/lucide-react/dist/esm/icons/sun.js", "../node_modules/lucide-react/dist/esm/icons/trending-up.js", "../node_modules/lucide-react/dist/esm/icons/users.js", "../node_modules/lucide-react/dist/esm/icons/x.js", "../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fgiopff%2FDownloads%2Fus.sitesucker.mac.sitesucker%2FTG%2Fadmin-panel%2Fpages%2Findex.tsx&page=%2F!", "../node_modules/next/dist/client/components/router-reducer/router-reducer-types.js", "../node_modules/next/dist/client/get-domain-locale.js", "../node_modules/next/dist/client/link.js", "../node_modules/next/dist/client/use-intersection.js", "../node_modules/next/link.js", "./components/Layout.tsx", "./components/LoadingSpinner.tsx", "./components/TeleShopDashboard.tsx", "./pages/index.tsx", "__barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!../node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=BarChart3,Bell,LayoutDashboard,LifeBuoy,LogOut,Menu,Moon,Package,Search,Settings,ShoppingCart,Sun,Users,X!=!../node_modules/lucide-react/dist/esm/lucide-react.js"]}