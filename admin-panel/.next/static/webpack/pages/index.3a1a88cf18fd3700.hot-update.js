"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/TeleShopDashboard.tsx":
/*!******************************************!*\
  !*** ./components/TeleShopDashboard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst TeleShopDashboard = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalOrders: 0,\n        totalRevenue: 0,\n        totalCustomers: 0,\n        conversionRate: 0,\n        orderGrowth: 0,\n        revenueGrowth: 0,\n        customerGrowth: 0,\n        conversionGrowth: 0\n    });\n    const [topProducts, setTopProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revenueData, setRevenueData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Simulate API calls with mock data for now\n            setTimeout(()=>{\n                setStats({\n                    totalOrders: 156,\n                    totalRevenue: 12426,\n                    totalCustomers: 834,\n                    conversionRate: 3.6,\n                    orderGrowth: 12.5,\n                    revenueGrowth: 8.2,\n                    customerGrowth: 4.6,\n                    conversionGrowth: -1.2\n                });\n                setTopProducts([\n                    {\n                        id: \"1\",\n                        name: \"Premium Headphones\",\n                        revenue: 4800,\n                        sales: 48\n                    },\n                    {\n                        id: \"2\",\n                        name: \"Wireless Earbuds\",\n                        revenue: 3360,\n                        sales: 42\n                    },\n                    {\n                        id: \"3\",\n                        name: \"Smartphone Case\",\n                        revenue: 720,\n                        sales: 36\n                    },\n                    {\n                        id: \"4\",\n                        name: \"Smart Watch\",\n                        revenue: 3600,\n                        sales: 30\n                    },\n                    {\n                        id: \"5\",\n                        name: \"Power Bank 10000mAh\",\n                        revenue: 960,\n                        sales: 24\n                    }\n                ]);\n                setRevenueData([\n                    {\n                        day: \"Mon\",\n                        revenue: 1000,\n                        profit: 800\n                    },\n                    {\n                        day: \"Tue\",\n                        revenue: 1500,\n                        profit: 1200\n                    },\n                    {\n                        day: \"Wed\",\n                        revenue: 2000,\n                        profit: 1600\n                    },\n                    {\n                        day: \"Thu\",\n                        revenue: 2500,\n                        profit: 1800\n                    },\n                    {\n                        day: \"Fri\",\n                        revenue: 3000,\n                        profit: 2200\n                    },\n                    {\n                        day: \"Sat\",\n                        revenue: 4500,\n                        profit: 2500\n                    },\n                    {\n                        day: \"Sun\",\n                        revenue: 3500,\n                        profit: 2000\n                    }\n                ]);\n                setLoading(false);\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const StatCard = (param)=>{\n        let { title, value, growth, icon, prefix = \"\", suffix = \"\", iconBg = \"bg-blue-500\" } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"stat-card group\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium\",\n                                    style: {\n                                        color: \"var(--text-secondary)\"\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-lg \".concat(iconBg),\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold mb-3\",\n                            style: {\n                                color: \"var(--text-primary)\"\n                            },\n                            children: [\n                                prefix,\n                                typeof value === \"number\" ? value.toLocaleString() : value,\n                                suffix\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                growth >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowUpIcon, {\n                                    className: \"w-4 h-4 text-green-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDownIcon, {\n                                    className: \"w-4 h-4 text-red-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium \".concat(growth >= 0 ? \"text-green-400\" : \"text-red-400\"),\n                                    children: [\n                                        growth >= 0 ? \"+\" : \"\",\n                                        growth,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    style: {\n                                        color: \"var(--text-tertiary)\"\n                                    },\n                                    children: \"from last period\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 110,\n            columnNumber: 5\n        }, undefined);\n    };\n    const SimpleChart = (param)=>{\n        let { data } = param;\n        const maxValue = Math.max(...data.map((d)=>Math.max(d.revenue, d.profit)));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 flex items-end justify-between px-4 py-4\",\n            children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center flex-1 mx-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-end h-48 w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-blue-500 rounded-t-sm mr-1\",\n                                    style: {\n                                        height: \"\".concat(item.revenue / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-green-500 rounded-t-sm ml-1 absolute bottom-0\",\n                                    style: {\n                                        height: \"\".concat(item.profit / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs mt-2\",\n                            style: {\n                                color: \"var(--text-secondary)\"\n                            },\n                            children: item.day\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, item.day, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Orders\",\n                        value: stats.totalOrders,\n                        growth: stats.orderGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShoppingBagIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Revenue\",\n                        value: stats.totalRevenue,\n                        growth: stats.revenueGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyDollarIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 17\n                        }, void 0),\n                        prefix: \"$\",\n                        iconBg: \"bg-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active Customers\",\n                        value: stats.totalCustomers,\n                        growth: stats.customerGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Conversion Rate\",\n                        value: stats.conversionRate,\n                        growth: stats.conversionGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingUpIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 17\n                        }, void 0),\n                        suffix: \"%\",\n                        iconBg: \"bg-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        style: {\n                                            color: \"var(--text-primary)\"\n                                        },\n                                        children: \"Revenue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Profit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleChart, {\n                                data: revenueData\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-6\",\n                                style: {\n                                    color: \"var(--text-primary)\"\n                                },\n                                children: \"Top Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: topProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium w-6 h-6 flex items-center justify-center rounded-full bg-blue-500 text-white mr-3\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                style: {\n                                                                    color: \"var(--text-primary)\"\n                                                                },\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                style: {\n                                                                    color: \"var(--text-secondary)\"\n                                                                },\n                                                                children: [\n                                                                    product.sales,\n                                                                    \" sold\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        style: {\n                                                            color: \"var(--text-primary)\"\n                                                        },\n                                                        children: [\n                                                            \"$\",\n                                                            product.revenue\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeleShopDashboard, \"/MjSXFkUfTmQBKMxalkGXohaCL8=\");\n_c = TeleShopDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeleShopDashboard);\nvar _c;\n$RefreshReg$(_c, \"TeleShopDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TeleShopDashboard.tsx\n"));

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var _components_TeleShopDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TeleShopDashboard */ \"./components/TeleShopDashboard.tsx\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/pages/index.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/pages/index.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeleShopDashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/pages/index.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/pages/index.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Zr2WDa/YWeMetzDhcnOimt0LiKE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});