"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Dashboard.tsx":
/*!**********************************!*\
  !*** ./components/Dashboard.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"../node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api */ \"./services/api.ts\");\n/* harmony import */ var _ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ecommerce/shared */ \"../shared/dist/index.js\");\n/* harmony import */ var _ecommerce_shared__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,DollarSign,Package,ShoppingCart,Users,XCircle!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Clock,DollarSign,Package,ShoppingCart,Users,XCircle!=!../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"./components/LoadingSpinner.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { data: dashboardData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"dashboard\"\n        ],\n        queryFn: ()=>_services_api__WEBPACK_IMPORTED_MODULE_1__.apiService.getDashboardData(),\n        refetchInterval: 30000\n    });\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !(dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.success)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-600\",\n                children: \"Failed to load dashboard data\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this);\n    }\n    const dashboard = dashboardData.data.dashboard;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-2\",\n                        children: \"Welcome to your digital store admin panel\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Today's Revenue\",\n                        value: (0,_ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(dashboard.today.revenue, \"USD\"),\n                        icon: _barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.DollarSign,\n                        color: \"green\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Today's Orders\",\n                        value: dashboard.today.orders.toString(),\n                        icon: _barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ShoppingCart,\n                        color: \"blue\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active Products\",\n                        value: dashboard.overall.activeProducts.toString(),\n                        icon: _barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Package,\n                        color: \"purple\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Pending Orders\",\n                        value: dashboard.overall.pendingOrders.toString(),\n                        icon: _barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Clock,\n                        color: \"orange\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Package, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ShoppingCart, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"View Orders\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Users, {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Manage Users\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"This Month\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Revenue\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: (0,_ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(dashboard.thisMonth.revenue, \"USD\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: dashboard.thisMonth.orders\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"New Customers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: dashboard.thisMonth.customers\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Store Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Total Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: dashboard.overall.totalProducts\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Active Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-green-600\",\n                                                children: dashboard.overall.activeProducts\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Pending Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-orange-600\",\n                                                children: dashboard.overall.pendingOrders\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Recent Orders\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Order ID\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Customer\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: dashboard.recentOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"font-mono text-sm\",\n                                                    children: [\n                                                        \"#\",\n                                                        order.id.substring(0, 8)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: order.user.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: (0,_ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(order.totalAmount, order.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusBadgeColor(order.status)),\n                                                        children: [\n                                                            getStatusIcon(order.status),\n                                                            order.status\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: (0,_ecommerce_shared__WEBPACK_IMPORTED_MODULE_2__.formatDate)(order.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, order.id, true, {\n                                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OkN4kYPRa1irvoMYcMY2fg5KsTA=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery\n    ];\n});\n_c = Dashboard;\nfunction StatCard(param) {\n    let { title, value, icon: Icon, color } = param;\n    const colorClasses = {\n        green: \"bg-green-500\",\n        blue: \"bg-blue-500\",\n        purple: \"bg-purple-500\",\n        orange: \"bg-orange-500\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 rounded-lg \".concat(colorClasses[color]),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-6 w-6 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-600\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-bold text-gray-900 mt-1\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nfunction getStatusBadgeColor(status) {\n    switch(status){\n        case \"DELIVERED\":\n            return \"bg-green-100 text-green-800\";\n        case \"PAID\":\n            return \"bg-blue-100 text-blue-800\";\n        case \"PENDING\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"CANCELLED\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n}\nfunction getStatusIcon(status) {\n    switch(status){\n        case \"DELIVERED\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 215,\n                columnNumber: 30\n            }, this);\n        case \"PAID\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 216,\n                columnNumber: 25\n            }, this);\n        case \"PENDING\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Clock, {\n                className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 217,\n                columnNumber: 28\n            }, this);\n        case \"CANCELLED\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_DollarSign_Package_ShoppingCart_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/Dashboard.tsx\",\n                lineNumber: 218,\n                columnNumber: 30\n            }, this);\n        default:\n            return null;\n    }\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c1, \"StatCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Dashboard.tsx\n"));

/***/ })

});