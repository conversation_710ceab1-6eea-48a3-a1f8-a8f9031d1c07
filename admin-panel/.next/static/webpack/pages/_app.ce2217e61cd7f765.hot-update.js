"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n/*\\nRemove the default font size and weight for headings.\\n*/\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n/*\\nAdd the correct font size in all browsers.\\n*/\\nsmall {\\n  font-size: 80%;\\n}\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\nsub {\\n  bottom: -0.25em;\\n}\\nsup {\\n  top: -0.5em;\\n}\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n:-moz-focusring {\\n  outline: auto;\\n}\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\nprogress {\\n  vertical-align: baseline;\\n}\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\nsummary {\\n  display: list-item;\\n}\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\nlegend {\\n  padding: 0;\\n}\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\ntextarea {\\n  resize: vertical;\\n}\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n/*\\nSet the default cursor for buttons.\\n*/\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n* {\\n    box-sizing: border-box;\\n  }\\nhtml {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\nbody {\\n    background-color: var(--bg-secondary);\\n    color: var(--text-primary);\\n    transition: background-color 0.3s ease, color 0.3s ease;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n/* TeleShop Button Components */\\n.btn{\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.5rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.btn:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n}\\n.btn:disabled{\\n  cursor: not-allowed;\\n  opacity: 0.5;\\n}\\n.btn-primary {\\n    background-color: var(--primary);\\n    color: white;\\n  }\\n.btn-primary:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-primary:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.btn-primary:hover {\\n    background-color: var(--primary-hover);\\n  }\\n.btn-secondary {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n  }\\n.btn-secondary:hover{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.btn-secondary:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.btn-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n  }\\n/* TeleShop Card Components */\\n.card {\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    border-radius: 0.5rem;\\n    padding: 1.5rem;\\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n    transition-property: all;\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    transition-duration: 200ms;\\n  }\\n.card-hover:hover{\\n  --tw-translate-y: -1px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.stat-card {\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    border-radius: 0.5rem;\\n    padding: 1.5rem;\\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n    transition-property: all;\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    transition-duration: 200ms;\\n  }\\n.stat-card:hover{\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n/* TeleShop Input Components */\\n.input {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n    display: block;\\n    width: 100%;\\n    border-radius: 0.5rem;\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n.input::-moz-placeholder{\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.input::placeholder{\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.input{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.input:focus{\\n  border-color: transparent;\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.select {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n    display: block;\\n    width: 100%;\\n    border-radius: 0.5rem;\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n    transition-property: all;\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    transition-duration: 200ms;\\n  }\\n.select:focus{\\n  border-color: transparent;\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n/* TeleShop Table Styles */\\n.table-teleshop{\\n  width: 100%;\\n  border-collapse: collapse;\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    border-radius: 8px;\\n    overflow: hidden;\\n}\\n.table-teleshop th {\\n    color: var(--text-secondary);\\n    background-color: var(--bg-tertiary);\\n    border-bottom: 1px solid var(--border-color);\\n    padding-top: 1rem;\\n    padding-bottom: 1rem;\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n    text-align: left;\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n    font-weight: 600;\\n    text-transform: uppercase;\\n    letter-spacing: 0.025em;\\n  }\\n.table-teleshop td {\\n    color: var(--text-primary);\\n    border-bottom: 1px solid var(--border-color);\\n    padding-top: 1rem;\\n    padding-bottom: 1rem;\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n.table-teleshop tr:hover {\\n    background-color: var(--bg-tertiary);\\n  }\\n.table-teleshop tr:last-child td {\\n    border-bottom: none;\\n  }\\n/* Status Badges */\\n.badge{\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n.badge-success {\\n    background-color: rgba(72, 187, 120, 0.1);\\n    color: var(--success-light);\\n  }\\n.badge-warning {\\n    background-color: rgba(237, 137, 54, 0.1);\\n    color: var(--warning-light);\\n  }\\n.badge-error {\\n    background-color: rgba(245, 101, 101, 0.1);\\n    color: var(--error-light);\\n  }\\n.badge-info {\\n    background-color: rgba(66, 153, 225, 0.1);\\n    color: var(--info-light);\\n  }\\n/* Sidebar Styles */\\n.sidebar {\\n    background-color: var(--bg-sidebar);\\n    border-right: 1px solid var(--border-color);\\n  }\\n.sidebar-item {\\n    color: var(--text-secondary);\\n    display: flex;\\n    align-items: center;\\n    border-radius: 0.5rem;\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n    font-weight: 500;\\n    transition-property: all;\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    transition-duration: 200ms;\\n  }\\n.sidebar-item:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n    color: var(--text-primary);\\n}\\n.sidebar-item.active {\\n    background-color: var(--primary-bg);\\n    color: var(--primary-light);\\n  }\\n/* Utility Classes */\\n.gradient-text{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  color: transparent;\\n}\\n/* TeleShop Animations */\\n.animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n.animate-slide-up {\\n    animation: slideUp 0.6s ease-out;\\n  }\\n@keyframes fadeIn {\\n    from {\\n      opacity: 0;\\n    }\\n    to {\\n      opacity: 1;\\n    }\\n  }\\n@keyframes slideUp {\\n    from {\\n      opacity: 0;\\n      transform: translateY(20px);\\n    }\\n    to {\\n      opacity: 1;\\n      transform: translateY(0);\\n    }\\n  }\\n@keyframes scaleIn {\\n    from {\\n      opacity: 0;\\n      transform: scale(0.95);\\n    }\\n    to {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n  }\\n/* Hover Effects */\\n/* Loading States */\\n@keyframes shimmer {\\n    0% {\\n      background-position: -200% 0;\\n    }\\n    100% {\\n      background-position: 200% 0;\\n    }\\n  }\\n.pointer-events-none{\\n  pointer-events: none;\\n}\\n.visible{\\n  visibility: visible;\\n}\\n.static{\\n  position: static;\\n}\\n.fixed{\\n  position: fixed;\\n}\\n.absolute{\\n  position: absolute;\\n}\\n.relative{\\n  position: relative;\\n}\\n.sticky{\\n  position: sticky;\\n}\\n.inset-0{\\n  inset: 0px;\\n}\\n.inset-y-0{\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-bottom-40{\\n  bottom: -10rem;\\n}\\n.-left-40{\\n  left: -10rem;\\n}\\n.-right-40{\\n  right: -10rem;\\n}\\n.-top-40{\\n  top: -10rem;\\n}\\n.bottom-0{\\n  bottom: 0px;\\n}\\n.left-0{\\n  left: 0px;\\n}\\n.left-3{\\n  left: 0.75rem;\\n}\\n.right-0{\\n  right: 0px;\\n}\\n.right-1{\\n  right: 0.25rem;\\n}\\n.top-0{\\n  top: 0px;\\n}\\n.top-1{\\n  top: 0.25rem;\\n}\\n.top-1\\\\/2{\\n  top: 50%;\\n}\\n.z-10{\\n  z-index: 10;\\n}\\n.z-30{\\n  z-index: 30;\\n}\\n.z-40{\\n  z-index: 40;\\n}\\n.z-50{\\n  z-index: 50;\\n}\\n.mx-1{\\n  margin-left: 0.25rem;\\n  margin-right: 0.25rem;\\n}\\n.mx-auto{\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.my-8{\\n  margin-top: 2rem;\\n  margin-bottom: 2rem;\\n}\\n.-mb-px{\\n  margin-bottom: -1px;\\n}\\n.-mr-12{\\n  margin-right: -3rem;\\n}\\n.mb-1{\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2{\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3{\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4{\\n  margin-bottom: 1rem;\\n}\\n.mb-6{\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8{\\n  margin-bottom: 2rem;\\n}\\n.ml-1{\\n  margin-left: 0.25rem;\\n}\\n.ml-2{\\n  margin-left: 0.5rem;\\n}\\n.mr-1{\\n  margin-right: 0.25rem;\\n}\\n.mr-2{\\n  margin-right: 0.5rem;\\n}\\n.mr-3{\\n  margin-right: 0.75rem;\\n}\\n.mr-8{\\n  margin-right: 2rem;\\n}\\n.mt-0\\\\.5{\\n  margin-top: 0.125rem;\\n}\\n.mt-1{\\n  margin-top: 0.25rem;\\n}\\n.mt-2{\\n  margin-top: 0.5rem;\\n}\\n.mt-3{\\n  margin-top: 0.75rem;\\n}\\n.mt-4{\\n  margin-top: 1rem;\\n}\\n.mt-6{\\n  margin-top: 1.5rem;\\n}\\n.block{\\n  display: block;\\n}\\n.inline-block{\\n  display: inline-block;\\n}\\n.flex{\\n  display: flex;\\n}\\n.inline-flex{\\n  display: inline-flex;\\n}\\n.table{\\n  display: table;\\n}\\n.grid{\\n  display: grid;\\n}\\n.hidden{\\n  display: none;\\n}\\n.h-10{\\n  height: 2.5rem;\\n}\\n.h-12{\\n  height: 3rem;\\n}\\n.h-16{\\n  height: 4rem;\\n}\\n.h-2{\\n  height: 0.5rem;\\n}\\n.h-20{\\n  height: 5rem;\\n}\\n.h-3{\\n  height: 0.75rem;\\n}\\n.h-32{\\n  height: 8rem;\\n}\\n.h-4{\\n  height: 1rem;\\n}\\n.h-48{\\n  height: 12rem;\\n}\\n.h-5{\\n  height: 1.25rem;\\n}\\n.h-6{\\n  height: 1.5rem;\\n}\\n.h-64{\\n  height: 16rem;\\n}\\n.h-8{\\n  height: 2rem;\\n}\\n.h-80{\\n  height: 20rem;\\n}\\n.min-h-0{\\n  min-height: 0px;\\n}\\n.min-h-screen{\\n  min-height: 100vh;\\n}\\n.w-10{\\n  width: 2.5rem;\\n}\\n.w-12{\\n  width: 3rem;\\n}\\n.w-16{\\n  width: 4rem;\\n}\\n.w-2{\\n  width: 0.5rem;\\n}\\n.w-3{\\n  width: 0.75rem;\\n}\\n.w-32{\\n  width: 8rem;\\n}\\n.w-4{\\n  width: 1rem;\\n}\\n.w-5{\\n  width: 1.25rem;\\n}\\n.w-6{\\n  width: 1.5rem;\\n}\\n.w-64{\\n  width: 16rem;\\n}\\n.w-8{\\n  width: 2rem;\\n}\\n.w-80{\\n  width: 20rem;\\n}\\n.w-full{\\n  width: 100%;\\n}\\n.max-w-2xl{\\n  max-width: 42rem;\\n}\\n.max-w-7xl{\\n  max-width: 80rem;\\n}\\n.max-w-md{\\n  max-width: 28rem;\\n}\\n.max-w-xs{\\n  max-width: 20rem;\\n}\\n.flex-1{\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0{\\n  flex-shrink: 0;\\n}\\n.-translate-y-1\\\\/2{\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform{\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes fadeIn{\\n  0%{\\n    opacity: 0;\\n  }\\n  100%{\\n    opacity: 1;\\n  }\\n}\\n.animate-fade-in{\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n@keyframes pulse{\\n  50%{\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse{\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes slideUp{\\n  0%{\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  100%{\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n.animate-slide-up{\\n  animation: slideUp 0.3s ease-out;\\n}\\n@keyframes spin{\\n  to{\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin{\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed{\\n  cursor: not-allowed;\\n}\\n.cursor-pointer{\\n  cursor: pointer;\\n}\\n.resize-none{\\n  resize: none;\\n}\\n.list-inside{\\n  list-style-position: inside;\\n}\\n.list-decimal{\\n  list-style-type: decimal;\\n}\\n.grid-cols-1{\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.flex-col{\\n  flex-direction: column;\\n}\\n.items-start{\\n  align-items: flex-start;\\n}\\n.items-end{\\n  align-items: flex-end;\\n}\\n.items-center{\\n  align-items: center;\\n}\\n.justify-end{\\n  justify-content: flex-end;\\n}\\n.justify-center{\\n  justify-content: center;\\n}\\n.justify-between{\\n  justify-content: space-between;\\n}\\n.gap-4{\\n  gap: 1rem;\\n}\\n.gap-6{\\n  gap: 1.5rem;\\n}\\n.gap-8{\\n  gap: 2rem;\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.overflow-auto{\\n  overflow: auto;\\n}\\n.overflow-hidden{\\n  overflow: hidden;\\n}\\n.overflow-x-auto{\\n  overflow-x: auto;\\n}\\n.overflow-y-auto{\\n  overflow-y: auto;\\n}\\n.rounded{\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl{\\n  border-radius: 1rem;\\n}\\n.rounded-full{\\n  border-radius: 9999px;\\n}\\n.rounded-lg{\\n  border-radius: 0.5rem;\\n}\\n.rounded-md{\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl{\\n  border-radius: 0.75rem;\\n}\\n.rounded-t-sm{\\n  border-top-left-radius: 0.125rem;\\n  border-top-right-radius: 0.125rem;\\n}\\n.border{\\n  border-width: 1px;\\n}\\n.border-2{\\n  border-width: 2px;\\n}\\n.border-b{\\n  border-bottom-width: 1px;\\n}\\n.border-b-2{\\n  border-bottom-width: 2px;\\n}\\n.border-t{\\n  border-top-width: 1px;\\n}\\n.border-dashed{\\n  border-style: dashed;\\n}\\n.border-blue-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-600{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent{\\n  border-color: transparent;\\n}\\n.border-white{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-200{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.bg-black\\\\/20{\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\n.bg-black\\\\/50{\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-600{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-700{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-900{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-200{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-50{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-800{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-75{\\n  --tw-bg-opacity: 0.75;\\n}\\n.bg-gradient-to-b{\\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-br{\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-r{\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-blue-400{\\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-50{\\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-500{\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-emerald-500{\\n  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-indigo-400{\\n  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-indigo-500{\\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-orange-500{\\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-500{\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-indigo-50{\\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\\n}\\n.to-indigo-600{\\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\\n}\\n.to-pink-600{\\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\\n}\\n.to-purple-600{\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\n.to-red-600{\\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\\n}\\n.to-teal-600{\\n  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);\\n}\\n.p-0{\\n  padding: 0px;\\n}\\n.p-2{\\n  padding: 0.5rem;\\n}\\n.p-3{\\n  padding: 0.75rem;\\n}\\n.p-4{\\n  padding: 1rem;\\n}\\n.p-6{\\n  padding: 1.5rem;\\n}\\n.p-8{\\n  padding: 2rem;\\n}\\n.px-1{\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2{\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3{\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4{\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6{\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.px-8{\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-1{\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12{\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2{\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3{\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4{\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.pb-20{\\n  padding-bottom: 5rem;\\n}\\n.pb-4{\\n  padding-bottom: 1rem;\\n}\\n.pl-10{\\n  padding-left: 2.5rem;\\n}\\n.pl-12{\\n  padding-left: 3rem;\\n}\\n.pl-4{\\n  padding-left: 1rem;\\n}\\n.pr-12{\\n  padding-right: 3rem;\\n}\\n.pr-4{\\n  padding-right: 1rem;\\n}\\n.pt-2{\\n  padding-top: 0.5rem;\\n}\\n.pt-4{\\n  padding-top: 1rem;\\n}\\n.pt-6{\\n  padding-top: 1.5rem;\\n}\\n.text-left{\\n  text-align: left;\\n}\\n.text-center{\\n  text-align: center;\\n}\\n.text-right{\\n  text-align: right;\\n}\\n.align-middle{\\n  vertical-align: middle;\\n}\\n.font-mono{\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\n.text-2xl{\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl{\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl{\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-base{\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg{\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm{\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl{\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs{\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold{\\n  font-weight: 700;\\n}\\n.font-medium{\\n  font-weight: 500;\\n}\\n.font-semibold{\\n  font-weight: 600;\\n}\\n.text-blue-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500{\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400{\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-white{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600{\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800{\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-900{\\n  --tw-text-opacity: 1;\\n  color: rgb(113 63 18 / var(--tw-text-opacity, 1));\\n}\\n.underline{\\n  text-decoration-line: underline;\\n}\\n.opacity-20{\\n  opacity: 0.2;\\n}\\n.opacity-60{\\n  opacity: 0.6;\\n}\\n.shadow-lg{\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm{\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.filter{\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm{\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.backdrop-blur-xl{\\n  --tw-backdrop-blur: blur(24px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition-all{\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors{\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity{\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform{\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200{\\n  transition-duration: 200ms;\\n}\\n\\n/* TeleShop Dark Theme Variables */\\n:root {\\n  /* Dark Theme Primary Colors */\\n  --bg-primary: #0f1419;\\n  --bg-secondary: #1a1f2e;\\n  --bg-tertiary: #252b3b;\\n  --bg-quaternary: #2d3748;\\n  --bg-card: #1e2532;\\n  --bg-sidebar: #161b26;\\n\\n  /* Text Colors */\\n  --text-primary: #ffffff;\\n  --text-secondary: #a0aec0;\\n  --text-tertiary: #718096;\\n  --text-muted: #4a5568;\\n\\n  /* Border Colors */\\n  --border-color: #2d3748;\\n  --border-light: #4a5568;\\n  --border-focus: #4299e1;\\n\\n  /* Brand Colors */\\n  --primary: #4299e1;\\n  --primary-hover: #3182ce;\\n  --primary-light: #63b3ed;\\n  --primary-dark: #2b6cb0;\\n  --primary-bg: #1a365d;\\n\\n  /* Status Colors */\\n  --success: #48bb78;\\n  --success-light: #68d391;\\n  --warning: #ed8936;\\n  --warning-light: #f6ad55;\\n  --error: #f56565;\\n  --error-light: #fc8181;\\n  --info: #4299e1;\\n  --info-light: #63b3ed;\\n\\n  /* Accent Colors */\\n  --accent-blue: #4299e1;\\n  --accent-green: #48bb78;\\n  --accent-purple: #9f7aea;\\n  --accent-orange: #ed8936;\\n  --accent-red: #f56565;\\n\\n  /* Shadow Colors */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);\\n}\\n\\n.hover\\\\:border-gray-300:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n\\n.hover\\\\:border-gray-400:hover{\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\n\\n.hover\\\\:bg-blue-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-100:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-50:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-700:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-red-900\\\\/20:hover{\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n\\n.hover\\\\:bg-blue-600:hover{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:text-blue-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-blue-800:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-600:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-700:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-red-800:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-white:hover{\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.focus\\\\:outline-none:focus{\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus\\\\:ring-2:focus{\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus\\\\:ring-blue-500:focus{\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-white\\\\/50:focus{\\n  --tw-ring-color: rgb(255 255 255 / 0.5);\\n}\\n\\n.focus\\\\:ring-offset-2:focus{\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled{\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:opacity-50:disabled{\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:scale-105{\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.group:hover .group-hover\\\\:shadow-xl{\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.dark\\\\:border-blue-800:is(.dark *){\\n  --tw-border-opacity: 1;\\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-gray-700:is(.dark *){\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:border-yellow-800:is(.dark *){\\n  --tw-border-opacity: 1;\\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\\n}\\n\\n.dark\\\\:bg-blue-900\\\\/20:is(.dark *){\\n  background-color: rgb(30 58 138 / 0.2);\\n}\\n\\n.dark\\\\:bg-gray-800:is(.dark *){\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:bg-yellow-800:is(.dark *){\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:bg-yellow-900\\\\/20:is(.dark *){\\n  background-color: rgb(113 63 18 / 0.2);\\n}\\n\\n.dark\\\\:from-blue-900\\\\/20:is(.dark *){\\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.dark\\\\:to-indigo-900\\\\/20:is(.dark *){\\n  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);\\n}\\n\\n.dark\\\\:text-blue-100:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-blue-200:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-gray-300:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-gray-400:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-white:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-yellow-100:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(254 249 195 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:text-yellow-200:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:bg-gray-800:hover:is(.dark *){\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:text-gray-200:hover:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\n\\n.dark\\\\:hover\\\\:text-gray-300:hover:is(.dark *){\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n\\n@media (min-width: 640px){\\n  .sm\\\\:mt-0{\\n    margin-top: 0px;\\n  }\\n  .sm\\\\:block{\\n    display: block;\\n  }\\n  .sm\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n  .sm\\\\:flex-row{\\n    flex-direction: row;\\n  }\\n  .sm\\\\:items-center{\\n    align-items: center;\\n  }\\n  .sm\\\\:justify-between{\\n    justify-content: space-between;\\n  }\\n  .sm\\\\:p-0{\\n    padding: 0px;\\n  }\\n  .sm\\\\:px-6{\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n\\n@media (min-width: 768px){\\n  .md\\\\:col-span-2{\\n    grid-column: span 2 / span 2;\\n  }\\n  .md\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n  .md\\\\:grid-cols-3{\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\\n\\n@media (min-width: 1024px){\\n  .lg\\\\:fixed{\\n    position: fixed;\\n  }\\n  .lg\\\\:inset-y-0{\\n    top: 0px;\\n    bottom: 0px;\\n  }\\n  .lg\\\\:block{\\n    display: block;\\n  }\\n  .lg\\\\:flex{\\n    display: flex;\\n  }\\n  .lg\\\\:hidden{\\n    display: none;\\n  }\\n  .lg\\\\:w-72{\\n    width: 18rem;\\n  }\\n  .lg\\\\:grid-cols-2{\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n  .lg\\\\:grid-cols-4{\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n  .lg\\\\:flex-row{\\n    flex-direction: row;\\n  }\\n  .lg\\\\:flex-col{\\n    flex-direction: column;\\n  }\\n  .lg\\\\:items-center{\\n    align-items: center;\\n  }\\n  .lg\\\\:justify-between{\\n    justify-content: space-between;\\n  }\\n  .lg\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n  .lg\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n  .lg\\\\:px-8{\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n  .lg\\\\:pl-72{\\n    padding-left: 18rem;\\n  }\\n  .lg\\\\:text-left{\\n    text-align: left;\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,uGAAuG;AACvG;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;AAAd;;CAAc;AAAd;;;CAAc;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;AAAd;;EAAA,gBAAc;AAAA;AAAd;;;;;;;;CAAc;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;AAAd;;;;CAAc;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;AAAd;;CAAc;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,mBAAc;AAAA;AAAd;;;;;CAAc;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,cAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;AAAd;EAAA,eAAc;AAAA;AAAd;EAAA,WAAc;AAAA;AAAd;;;;CAAc;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;AAAd;;;;CAAc;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,oBAAc;AAAA;AAAd;;;CAAc;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,aAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,gBAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,wBAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,YAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,wBAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,kBAAc;AAAA;AAAd;;CAAc;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;AAAd;EAAA,UAAc;AAAA;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,gBAAc;AAAA;AAAd;;;CAAc;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,eAAc;AAAA;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;AAAd;;;;CAAc;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;AAAd;;CAAc;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;AAAd;IAAA,sBAAc;EAAA;AAAd;IAAA,2CAAc;IAAd,uBAAc;EAAA;AAAd;IAAA,qCAAc;IAAd,0BAAc;IAAd,uDAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;AA4EZ,+BAA+B;AAE7B;EAAA,oBAAyN;EAAzN,mBAAyN;EAAzN,uBAAyN;EAAzN,qBAAyN;EAAzN,kBAAyN;EAAzN,mBAAyN;EAAzN,qBAAyN;EAAzN,wBAAyN;EAAzN,mBAAyN;EAAzN,oBAAyN;EAAzN,gBAAyN;EAAzN,wBAAyN;EAAzN,wDAAyN;EAAzN;AAAyN;AAAzN;EAAA,8BAAyN;EAAzN,mBAAyN;EAAzN,2GAAyN;EAAzN,yGAAyN;EAAzN,4FAAyN;EAAzN;AAAyN;AAAzN;EAAA,mBAAyN;EAAzN;AAAyN;AAG3N;IACE,gCAAgC;IAChC,YAAY;EAEd;AADE;EAAA,+EAA0C;EAA1C,mGAA0C;EAA1C;AAA0C;AAA1C;EAAA,oBAA0C;EAA1C;AAA0C;AAG5C;IACE,sCAAsC;EACxC;AAEA;IACE,gCAAgC;IAChC,0BAA0B;IAC1B,qCAAqC;EAEvC;AADE;EAAA,+EAA0C;EAA1C,mGAA0C;EAA1C;AAA0C;AAA1C;EAAA,oBAA0C;EAA1C;AAA0C;AAG5C;IACE,oCAAoC;EACtC;AA8BA,6BAA6B;AAC7B;IACE,gCAAgC;IAChC,qCAAqC;IACrC,qBAA2D;IAA3D,eAA2D;IAA3D,0CAA2D;IAA3D,uDAA2D;IAA3D,uGAA2D;IAA3D,wBAA2D;IAA3D,wDAA2D;IAA3D,0BAA2D;EAC7D;AAGE;EAAA,sBAA6C;EAA7C,+LAA6C;EAA7C,6EAA6C;EAA7C,iGAA6C;EAA7C;AAA6C;AAG/C;IACE,gCAAgC;IAChC,qCAAqC;IACrC,qBAA2E;IAA3E,eAA2E;IAA3E,0CAA2E;IAA3E,uDAA2E;IAA3E,uGAA2E;IAA3E,wBAA2E;IAA3E,wDAA2E;IAA3E,0BAA2E;EAC7E;AADE;EAAA,6EAA2E;EAA3E,iGAA2E;EAA3E;AAA2E;AAG7E,8BAA8B;AAC9B;IACE,gCAAgC;IAChC,0BAA0B;IAC1B,qCAAqC;IACrC,cAAsK;IAAtK,WAAsK;IAAtK,qBAAsK;IAAtK,kBAAsK;IAAtK,mBAAsK;IAAtK,oBAAsK;IAAtK,uBAAsK;EACxK;AADE;EAAA,2BAAsK;EAAtK;AAAsK;AAAtK;EAAA,2BAAsK;EAAtK;AAAsK;AAAtK;EAAA,wBAAsK;EAAtK,wDAAsK;EAAtK;AAAsK;AAAtK;EAAA,yBAAsK;EAAtK,8BAAsK;EAAtK,mBAAsK;EAAtK,2GAAsK;EAAtK,yGAAsK;EAAtK,4FAAsK;EAAtK,oBAAsK;EAAtK;AAAsK;AAGxK;IACE,gCAAgC;IAChC,0BAA0B;IAC1B,qCAAqC;IACrC,cAAiJ;IAAjJ,WAAiJ;IAAjJ,qBAAiJ;IAAjJ,kBAAiJ;IAAjJ,mBAAiJ;IAAjJ,oBAAiJ;IAAjJ,uBAAiJ;IAAjJ,wBAAiJ;IAAjJ,wDAAiJ;IAAjJ,0BAAiJ;EACnJ;AADE;EAAA,yBAAiJ;EAAjJ,8BAAiJ;EAAjJ,mBAAiJ;EAAjJ,2GAAiJ;EAAjJ,yGAAiJ;EAAjJ,4FAAiJ;EAAjJ,oBAAiJ;EAAjJ;AAAiJ;AAGnJ,0BAA0B;AAExB;EAAA,WAA6B;EAA7B,yBAA6B;IAC7B,gCAAgC;IAChC,qCAAqC;IACrC,kBAAkB;IAClB;AAJ6B;AAO/B;IACE,4BAA4B;IAC5B,oCAAoC;IACpC,4CAA4C;IAC5C,iBAAwE;IAAxE,oBAAwE;IAAxE,oBAAwE;IAAxE,qBAAwE;IAAxE,gBAAwE;IAAxE,mBAAwE;IAAxE,oBAAwE;IAAxE,gBAAwE;IAAxE,yBAAwE;IAAxE,uBAAwE;EAC1E;AAEA;IACE,0BAA0B;IAC1B,4CAA4C;IAC5C,iBAAwB;IAAxB,oBAAwB;IAAxB,oBAAwB;IAAxB,qBAAwB;IAAxB,mBAAwB;IAAxB,oBAAwB;EAC1B;AAEA;IACE,oCAAoC;EACtC;AAEA;IACE,mBAAmB;EACrB;AAEA,kBAAkB;AAEhB;EAAA,oBAA8E;EAA9E,mBAA8E;EAA9E,qBAA8E;EAA9E,sBAA8E;EAA9E,uBAA8E;EAA9E,qBAA8E;EAA9E,wBAA8E;EAA9E,kBAA8E;EAA9E,iBAA8E;EAA9E;AAA8E;AAGhF;IACE,yCAAyC;IACzC,2BAA2B;EAC7B;AAEA;IACE,yCAAyC;IACzC,2BAA2B;EAC7B;AAEA;IACE,0CAA0C;IAC1C,yBAAyB;EAC3B;AAEA;IACE,yCAAyC;IACzC,wBAAwB;EAC1B;AAEA,mBAAmB;AACnB;IACE,mCAAmC;IACnC,2CAA2C;EAC7C;AAEA;IACE,4BAA4B;IAC5B,aAA+G;IAA/G,mBAA+G;IAA/G,qBAA+G;IAA/G,kBAA+G;IAA/G,mBAA+G;IAA/G,oBAA+G;IAA/G,uBAA+G;IAA/G,mBAA+G;IAA/G,oBAA+G;IAA/G,gBAA+G;IAA/G,wBAA+G;IAA/G,wDAA+G;IAA/G,0BAA+G;EACjH;AADE;EAAA,kBAA+G;EAA/G,yDAA+G;IAI/G;AAJ+G;AAOjH;IACE,mCAAmC;IACnC,2BAA2B;EAC7B;AAEA,oBAAoB;AAElB;EAAA,qEAA+E;EAA/E,4DAA+E;EAA/E,oEAA+E;EAA/E,mEAA+E;EAA/E,wDAA+E;EAA/E,6BAA+E;UAA/E,qBAA+E;EAA/E;AAA+E;AAmBjF,wBAAwB;AACxB;IACE,kCAAkC;EACpC;AAEA;IACE,gCAAgC;EAClC;AAMA;IACE;MACE,UAAU;IACZ;IACA;MACE,UAAU;IACZ;EACF;AAEA;IACE;MACE,UAAU;MACV,2BAA2B;IAC7B;IACA;MACE,UAAU;MACV,wBAAwB;IAC1B;EACF;AAEA;IACE;MACE,UAAU;MACV,sBAAsB;IACxB;IACA;MACE,UAAU;MACV,mBAAmB;IACrB;EACF;AAEA,kBAAkB;AAWlB,mBAAmB;AAOnB;IACE;MACE,4BAA4B;IAC9B;IACA;MACE,2BAA2B;IAC7B;EACF;AArUF;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;IAAA;EAAmB;EAAnB;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;EAAnB;IAAA,wBAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;;AAEnB,kCAAkC;AAClC;EACE,8BAA8B;EAC9B,qBAAqB;EACrB,uBAAuB;EACvB,sBAAsB;EACtB,wBAAwB;EACxB,kBAAkB;EAClB,qBAAqB;;EAErB,gBAAgB;EAChB,uBAAuB;EACvB,yBAAyB;EACzB,wBAAwB;EACxB,qBAAqB;;EAErB,kBAAkB;EAClB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;;EAEvB,iBAAiB;EACjB,kBAAkB;EAClB,wBAAwB;EACxB,wBAAwB;EACxB,uBAAuB;EACvB,qBAAqB;;EAErB,kBAAkB;EAClB,kBAAkB;EAClB,wBAAwB;EACxB,kBAAkB;EAClB,wBAAwB;EACxB,gBAAgB;EAChB,sBAAsB;EACtB,eAAe;EACf,qBAAqB;;EAErB,kBAAkB;EAClB,sBAAsB;EACtB,uBAAuB;EACvB,wBAAwB;EACxB,wBAAwB;EACxB,qBAAqB;;EAErB,kBAAkB;EAClB,2CAA2C;EAC3C,8CAA8C;EAC9C,gDAAgD;EAChD,gDAAgD;AAClD;;AAvDA;EAAA,sBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,sBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,8BA0UA;EA1UA;AA0UA;;AA1UA;EAAA,2GA0UA;EA1UA,yGA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,gFA0UA;EA1UA,oGA0UA;EA1UA;AA0UA;;AA1UA;EAAA,sBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,sBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,sBA0UA;EA1UA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA,yEA0UA;EA1UA,mEA0UA;EA1UA;AA0UA;;AA1UA;EAAA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,kBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA,oBA0UA;EA1UA;AA0UA;;AA1UA;EAAA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA,oBA0UA;IA1UA;EA0UA;AAAA;;AA1UA;EAAA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;AAAA;;AA1UA;EAAA;IAAA;EA0UA;EA1UA;IAAA,QA0UA;IA1UA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA,uBA0UA;IA1UA,oDA0UA;IA1UA;EA0UA;EA1UA;IAAA,uBA0UA;IA1UA,2DA0UA;IA1UA;EA0UA;EA1UA;IAAA,kBA0UA;IA1UA;EA0UA;EA1UA;IAAA;EA0UA;EA1UA;IAAA;EA0UA;AAAA\",\"sourcesContent\":[\"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');\\n@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* TeleShop Dark Theme Variables */\\n:root {\\n  /* Dark Theme Primary Colors */\\n  --bg-primary: #0f1419;\\n  --bg-secondary: #1a1f2e;\\n  --bg-tertiary: #252b3b;\\n  --bg-quaternary: #2d3748;\\n  --bg-card: #1e2532;\\n  --bg-sidebar: #161b26;\\n\\n  /* Text Colors */\\n  --text-primary: #ffffff;\\n  --text-secondary: #a0aec0;\\n  --text-tertiary: #718096;\\n  --text-muted: #4a5568;\\n\\n  /* Border Colors */\\n  --border-color: #2d3748;\\n  --border-light: #4a5568;\\n  --border-focus: #4299e1;\\n\\n  /* Brand Colors */\\n  --primary: #4299e1;\\n  --primary-hover: #3182ce;\\n  --primary-light: #63b3ed;\\n  --primary-dark: #2b6cb0;\\n  --primary-bg: #1a365d;\\n\\n  /* Status Colors */\\n  --success: #48bb78;\\n  --success-light: #68d391;\\n  --warning: #ed8936;\\n  --warning-light: #f6ad55;\\n  --error: #f56565;\\n  --error-light: #fc8181;\\n  --info: #4299e1;\\n  --info-light: #63b3ed;\\n\\n  /* Accent Colors */\\n  --accent-blue: #4299e1;\\n  --accent-green: #48bb78;\\n  --accent-purple: #9f7aea;\\n  --accent-orange: #ed8936;\\n  --accent-red: #f56565;\\n\\n  /* Shadow Colors */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);\\n}\\n\\n@layer base {\\n  * {\\n    box-sizing: border-box;\\n  }\\n\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n\\n  body {\\n    background-color: var(--bg-secondary);\\n    color: var(--text-primary);\\n    transition: background-color 0.3s ease, color 0.3s ease;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n}\\n\\n@layer components {\\n  /* TeleShop Button Components */\\n  .btn {\\n    @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\\n  }\\n\\n  .btn-primary {\\n    background-color: var(--primary);\\n    color: white;\\n    @apply hover:shadow-lg focus:ring-blue-500;\\n  }\\n\\n  .btn-primary:hover {\\n    background-color: var(--primary-hover);\\n  }\\n\\n  .btn-secondary {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n    @apply hover:shadow-lg focus:ring-blue-500;\\n  }\\n\\n  .btn-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n  }\\n\\n  .btn-ghost {\\n    color: var(--text-secondary);\\n    background-color: transparent;\\n    @apply hover:bg-gray-700 focus:ring-blue-500;\\n  }\\n\\n  .btn-ghost:hover {\\n    color: var(--text-primary);\\n  }\\n\\n  .btn-success {\\n    background-color: var(--success);\\n    color: white;\\n    @apply hover:shadow-lg;\\n  }\\n\\n  .btn-warning {\\n    background-color: var(--warning);\\n    color: white;\\n    @apply hover:shadow-lg;\\n  }\\n\\n  .btn-error {\\n    background-color: var(--error);\\n    color: white;\\n    @apply hover:shadow-lg;\\n  }\\n\\n  /* TeleShop Card Components */\\n  .card {\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    @apply rounded-lg p-6 shadow-sm transition-all duration-200;\\n  }\\n\\n  .card-hover:hover {\\n    @apply shadow-md transform translate-y-[-1px];\\n  }\\n\\n  .stat-card {\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    @apply rounded-lg p-6 shadow-sm transition-all duration-200 hover:shadow-md;\\n  }\\n\\n  /* TeleShop Input Components */\\n  .input {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n    @apply block w-full px-4 py-3 rounded-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;\\n  }\\n\\n  .select {\\n    background-color: var(--bg-card);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border-color);\\n    @apply block w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;\\n  }\\n\\n  /* TeleShop Table Styles */\\n  .table-teleshop {\\n    @apply w-full border-collapse;\\n    background-color: var(--bg-card);\\n    border: 1px solid var(--border-color);\\n    border-radius: 8px;\\n    overflow: hidden;\\n  }\\n\\n  .table-teleshop th {\\n    color: var(--text-secondary);\\n    background-color: var(--bg-tertiary);\\n    border-bottom: 1px solid var(--border-color);\\n    @apply text-left py-4 px-6 text-sm font-semibold uppercase tracking-wide;\\n  }\\n\\n  .table-teleshop td {\\n    color: var(--text-primary);\\n    border-bottom: 1px solid var(--border-color);\\n    @apply py-4 px-6 text-sm;\\n  }\\n\\n  .table-teleshop tr:hover {\\n    background-color: var(--bg-tertiary);\\n  }\\n\\n  .table-teleshop tr:last-child td {\\n    border-bottom: none;\\n  }\\n\\n  /* Status Badges */\\n  .badge {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\\n  }\\n\\n  .badge-success {\\n    background-color: rgba(72, 187, 120, 0.1);\\n    color: var(--success-light);\\n  }\\n\\n  .badge-warning {\\n    background-color: rgba(237, 137, 54, 0.1);\\n    color: var(--warning-light);\\n  }\\n\\n  .badge-error {\\n    background-color: rgba(245, 101, 101, 0.1);\\n    color: var(--error-light);\\n  }\\n\\n  .badge-info {\\n    background-color: rgba(66, 153, 225, 0.1);\\n    color: var(--info-light);\\n  }\\n\\n  /* Sidebar Styles */\\n  .sidebar {\\n    background-color: var(--bg-sidebar);\\n    border-right: 1px solid var(--border-color);\\n  }\\n\\n  .sidebar-item {\\n    color: var(--text-secondary);\\n    @apply flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-gray-700;\\n  }\\n\\n  .sidebar-item:hover {\\n    color: var(--text-primary);\\n  }\\n\\n  .sidebar-item.active {\\n    background-color: var(--primary-bg);\\n    color: var(--primary-light);\\n  }\\n\\n  /* Utility Classes */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent;\\n  }\\n\\n  .text-success {\\n    color: var(--success);\\n  }\\n\\n  .text-warning {\\n    color: var(--warning);\\n  }\\n\\n  .text-error {\\n    color: var(--error);\\n  }\\n\\n  .text-info {\\n    color: var(--info);\\n  }\\n\\n  /* TeleShop Animations */\\n  .animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n\\n  .animate-slide-up {\\n    animation: slideUp 0.6s ease-out;\\n  }\\n\\n  .animate-scale-in {\\n    animation: scaleIn 0.4s ease-out;\\n  }\\n\\n  @keyframes fadeIn {\\n    from {\\n      opacity: 0;\\n    }\\n    to {\\n      opacity: 1;\\n    }\\n  }\\n\\n  @keyframes slideUp {\\n    from {\\n      opacity: 0;\\n      transform: translateY(20px);\\n    }\\n    to {\\n      opacity: 1;\\n      transform: translateY(0);\\n    }\\n  }\\n\\n  @keyframes scaleIn {\\n    from {\\n      opacity: 0;\\n      transform: scale(0.95);\\n    }\\n    to {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n  }\\n\\n  /* Hover Effects */\\n  .hover-lift:hover {\\n    transform: translateY(-2px);\\n    transition: transform 0.2s ease;\\n  }\\n\\n  .hover-glow:hover {\\n    box-shadow: 0 0 20px rgba(66, 153, 225, 0.3);\\n    transition: box-shadow 0.3s ease;\\n  }\\n\\n  /* Loading States */\\n  .loading-shimmer {\\n    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%);\\n    background-size: 200% 100%;\\n    animation: shimmer 1.5s infinite;\\n  }\\n\\n  @keyframes shimmer {\\n    0% {\\n      background-position: -200% 0;\\n    }\\n    100% {\\n      background-position: 200% 0;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});