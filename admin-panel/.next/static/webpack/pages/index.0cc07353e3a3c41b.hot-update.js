"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "../node_modules/lucide-react/dist/esm/icons/dollar-sign.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/dollar-sign.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DollarSign; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst DollarSign = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"DollarSign\", [\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"2\", y2: \"22\", key: \"7eqyqh\" }],\n  [\"path\", { d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\", key: \"1b0p4s\" }]\n]);\n\n\n//# sourceMappingURL=dollar-sign.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9kb2xsYXItc2lnbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELG1CQUFtQixnRUFBZ0I7QUFDbkMsYUFBYSxzREFBc0Q7QUFDbkUsYUFBYSx1RUFBdUU7QUFDcEY7O0FBRWlDO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2RvbGxhci1zaWduLmpzP2QzMWYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBEb2xsYXJTaWduID0gY3JlYXRlTHVjaWRlSWNvbihcIkRvbGxhclNpZ25cIiwgW1xuICBbXCJsaW5lXCIsIHsgeDE6IFwiMTJcIiwgeDI6IFwiMTJcIiwgeTE6IFwiMlwiLCB5MjogXCIyMlwiLCBrZXk6IFwiN2VxeXFoXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNyA1SDkuNWEzLjUgMy41IDAgMCAwIDAgN2g1YTMuNSAzLjUgMCAwIDEgMCA3SDZcIiwga2V5OiBcIjFiMHA0c1wiIH1dXG5dKTtcblxuZXhwb3J0IHsgRG9sbGFyU2lnbiBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kb2xsYXItc2lnbi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\n"));

/***/ }),

/***/ "../node_modules/lucide-react/dist/esm/icons/shopping-bag.js":
/*!*******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/shopping-bag.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShoppingBag; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ShoppingBag = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ShoppingBag\", [\n  [\"path\", { d: \"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z\", key: \"hou9p0\" }],\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M16 10a4 4 0 0 1-8 0\", key: \"1ltviw\" }]\n]);\n\n\n//# sourceMappingURL=shopping-bag.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zaG9wcGluZy1iYWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxvQkFBb0IsZ0VBQWdCO0FBQ3BDLGFBQWEsd0VBQXdFO0FBQ3JGLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsMENBQTBDO0FBQ3ZEOztBQUVrQztBQUNsQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9zaG9wcGluZy1iYWcuanM/NzZjNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFNob3BwaW5nQmFnID0gY3JlYXRlTHVjaWRlSWNvbihcIlNob3BwaW5nQmFnXCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTYgMiAzIDZ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0yVjZsLTMtNFpcIiwga2V5OiBcImhvdTlwMFwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMyA2aDE4XCIsIGtleTogXCJkMHdtMGpcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE2IDEwYTQgNCAwIDAgMS04IDBcIiwga2V5OiBcIjFsdHZpd1wiIH1dXG5dKTtcblxuZXhwb3J0IHsgU2hvcHBpbmdCYWcgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2hvcHBpbmctYmFnLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../node_modules/lucide-react/dist/esm/icons/shopping-bag.js\n"));

/***/ }),

/***/ "../node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TrendingUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", [\n  [\"polyline\", { points: \"22 7 13.5 15.5 8.5 10.5 2 17\", key: \"126l90\" }],\n  [\"polyline\", { points: \"16 7 22 7 22 13\", key: \"kwv8wd\" }]\n]);\n\n\n//# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy90cmVuZGluZy11cC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELG1CQUFtQixnRUFBZ0I7QUFDbkMsaUJBQWlCLHVEQUF1RDtBQUN4RSxpQkFBaUIsMENBQTBDO0FBQzNEOztBQUVpQztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy90cmVuZGluZy11cC5qcz81NzA1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjI5NC4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgVHJlbmRpbmdVcCA9IGNyZWF0ZUx1Y2lkZUljb24oXCJUcmVuZGluZ1VwXCIsIFtcbiAgW1wicG9seWxpbmVcIiwgeyBwb2ludHM6IFwiMjIgNyAxMy41IDE1LjUgOC41IDEwLjUgMiAxN1wiLCBrZXk6IFwiMTI2bDkwXCIgfV0sXG4gIFtcInBvbHlsaW5lXCIsIHsgcG9pbnRzOiBcIjE2IDcgMjIgNyAyMiAxM1wiLCBrZXk6IFwia3d2OHdkXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBUcmVuZGluZ1VwIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyZW5kaW5nLXVwLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDown: function() { return /* reexport safe */ _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   ArrowUp: function() { return /* reexport safe */ _icons_arrow_up_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   DollarSign: function() { return /* reexport safe */ _icons_dollar_sign_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   ShoppingBag: function() { return /* reexport safe */ _icons_shopping_bag_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   TrendingUp: function() { return /* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   Users: function() { return /* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-down.js */ \"../node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _icons_arrow_up_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/arrow-up.js */ \"../node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _icons_dollar_sign_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/dollar-sign.js */ \"../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _icons_shopping_bag_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/shopping-bag.js */ \"../node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/trending-up.js */ \"../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/users.js */ \"../node_modules/lucide-react/dist/esm/icons/users.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0Rvd24sQXJyb3dVcCxEb2xsYXJTaWduLFNob3BwaW5nQmFnLFRyZW5kaW5nVXAsVXNlcnMhPSEuLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQzREO0FBQ0o7QUFDTTtBQUNFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2E3M2EiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93RG93biB9IGZyb20gXCIuL2ljb25zL2Fycm93LWRvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1VwIH0gZnJvbSBcIi4vaWNvbnMvYXJyb3ctdXAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb2xsYXJTaWduIH0gZnJvbSBcIi4vaWNvbnMvZG9sbGFyLXNpZ24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0JhZyB9IGZyb20gXCIuL2ljb25zL3Nob3BwaW5nLWJhZy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyZW5kaW5nVXAgfSBmcm9tIFwiLi9pY29ucy90cmVuZGluZy11cC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzIH0gZnJvbSBcIi4vaWNvbnMvdXNlcnMuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!../node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./components/TeleShopDashboard.tsx":
/*!******************************************!*\
  !*** ./components/TeleShopDashboard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"__barrel_optimize__?names=ArrowDown,ArrowUp,DollarSign,ShoppingBag,TrendingUp,Users!=!../node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst TeleShopDashboard = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalOrders: 0,\n        totalRevenue: 0,\n        totalCustomers: 0,\n        conversionRate: 0,\n        orderGrowth: 0,\n        revenueGrowth: 0,\n        customerGrowth: 0,\n        conversionGrowth: 0\n    });\n    const [topProducts, setTopProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revenueData, setRevenueData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Simulate API calls with mock data for now\n            setTimeout(()=>{\n                setStats({\n                    totalOrders: 156,\n                    totalRevenue: 12426,\n                    totalCustomers: 834,\n                    conversionRate: 3.6,\n                    orderGrowth: 12.5,\n                    revenueGrowth: 8.2,\n                    customerGrowth: 4.6,\n                    conversionGrowth: -1.2\n                });\n                setTopProducts([\n                    {\n                        id: \"1\",\n                        name: \"Premium Headphones\",\n                        revenue: 4800,\n                        sales: 48\n                    },\n                    {\n                        id: \"2\",\n                        name: \"Wireless Earbuds\",\n                        revenue: 3360,\n                        sales: 42\n                    },\n                    {\n                        id: \"3\",\n                        name: \"Smartphone Case\",\n                        revenue: 720,\n                        sales: 36\n                    },\n                    {\n                        id: \"4\",\n                        name: \"Smart Watch\",\n                        revenue: 3600,\n                        sales: 30\n                    },\n                    {\n                        id: \"5\",\n                        name: \"Power Bank 10000mAh\",\n                        revenue: 960,\n                        sales: 24\n                    }\n                ]);\n                setRevenueData([\n                    {\n                        day: \"Mon\",\n                        revenue: 1000,\n                        profit: 800\n                    },\n                    {\n                        day: \"Tue\",\n                        revenue: 1500,\n                        profit: 1200\n                    },\n                    {\n                        day: \"Wed\",\n                        revenue: 2000,\n                        profit: 1600\n                    },\n                    {\n                        day: \"Thu\",\n                        revenue: 2500,\n                        profit: 1800\n                    },\n                    {\n                        day: \"Fri\",\n                        revenue: 3000,\n                        profit: 2200\n                    },\n                    {\n                        day: \"Sat\",\n                        revenue: 4500,\n                        profit: 2500\n                    },\n                    {\n                        day: \"Sun\",\n                        revenue: 3500,\n                        profit: 2000\n                    }\n                ]);\n                setLoading(false);\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const StatCard = (param)=>{\n        let { title, value, growth, icon, prefix = \"\", suffix = \"\", iconBg = \"bg-blue-500\" } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"stat-card group\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium\",\n                                    style: {\n                                        color: \"var(--text-secondary)\"\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-lg \".concat(iconBg),\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold mb-3\",\n                            style: {\n                                color: \"var(--text-primary)\"\n                            },\n                            children: [\n                                prefix,\n                                typeof value === \"number\" ? value.toLocaleString() : value,\n                                suffix\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                growth >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ArrowUp, {\n                                    className: \"w-4 h-4 text-green-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ArrowDown, {\n                                    className: \"w-4 h-4 text-red-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium \".concat(growth >= 0 ? \"text-green-400\" : \"text-red-400\"),\n                                    children: [\n                                        growth >= 0 ? \"+\" : \"\",\n                                        growth,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    style: {\n                                        color: \"var(--text-tertiary)\"\n                                    },\n                                    children: \"from last period\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 110,\n            columnNumber: 5\n        }, undefined);\n    };\n    const SimpleChart = (param)=>{\n        let { data } = param;\n        const maxValue = Math.max(...data.map((d)=>Math.max(d.revenue, d.profit)));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 flex items-end justify-between px-4 py-4\",\n            children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center flex-1 mx-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-end h-48 w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-blue-500 rounded-t-sm mr-1\",\n                                    style: {\n                                        height: \"\".concat(item.revenue / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-green-500 rounded-t-sm ml-1 absolute bottom-0\",\n                                    style: {\n                                        height: \"\".concat(item.profit / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs mt-2\",\n                            style: {\n                                color: \"var(--text-secondary)\"\n                            },\n                            children: item.day\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, item.day, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Orders\",\n                        value: stats.totalOrders,\n                        growth: stats.orderGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ShoppingBag, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Revenue\",\n                        value: stats.totalRevenue,\n                        growth: stats.revenueGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.DollarSign, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 17\n                        }, void 0),\n                        prefix: \"$\",\n                        iconBg: \"bg-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active Customers\",\n                        value: stats.totalCustomers,\n                        growth: stats.customerGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Users, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Conversion Rate\",\n                        value: stats.conversionRate,\n                        growth: stats.conversionGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_DollarSign_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__.TrendingUp, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 17\n                        }, void 0),\n                        suffix: \"%\",\n                        iconBg: \"bg-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        style: {\n                                            color: \"var(--text-primary)\"\n                                        },\n                                        children: \"Revenue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Profit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleChart, {\n                                data: revenueData\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-6\",\n                                style: {\n                                    color: \"var(--text-primary)\"\n                                },\n                                children: \"Top Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: topProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium w-6 h-6 flex items-center justify-center rounded-full bg-blue-500 text-white mr-3\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                style: {\n                                                                    color: \"var(--text-primary)\"\n                                                                },\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                style: {\n                                                                    color: \"var(--text-secondary)\"\n                                                                },\n                                                                children: [\n                                                                    product.sales,\n                                                                    \" sold\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        style: {\n                                                            color: \"var(--text-primary)\"\n                                                        },\n                                                        children: [\n                                                            \"$\",\n                                                            product.revenue\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeleShopDashboard, \"/MjSXFkUfTmQBKMxalkGXohaCL8=\");\n_c = TeleShopDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeleShopDashboard);\nvar _c;\n$RefreshReg$(_c, \"TeleShopDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TeleShopDashboard.tsx\n"));

/***/ })

});