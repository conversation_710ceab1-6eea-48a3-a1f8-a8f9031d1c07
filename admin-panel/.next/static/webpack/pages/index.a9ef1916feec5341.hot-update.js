"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "../node_modules/lucide-react/dist/esm/icons/arrow-down.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/arrow-down.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArrowDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ArrowDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowDown\", [\n  [\"path\", { d: \"M12 5v14\", key: \"s699le\" }],\n  [\"path\", { d: \"m19 12-7 7-7-7\", key: \"1idqje\" }]\n]);\n\n\n//# sourceMappingURL=arrow-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9hcnJvdy1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsa0JBQWtCLGdFQUFnQjtBQUNsQyxhQUFhLDhCQUE4QjtBQUMzQyxhQUFhLG9DQUFvQztBQUNqRDs7QUFFZ0M7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXJyb3ctZG93bi5qcz9kZjQ1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjI5NC4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgQXJyb3dEb3duID0gY3JlYXRlTHVjaWRlSWNvbihcIkFycm93RG93blwiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xMiA1djE0XCIsIGtleTogXCJzNjk5bGVcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTE5IDEyLTcgNy03LTdcIiwga2V5OiBcIjFpZHFqZVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQXJyb3dEb3duIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFycm93LWRvd24uanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../node_modules/lucide-react/dist/esm/icons/arrow-down.js\n"));

/***/ }),

/***/ "../node_modules/lucide-react/dist/esm/icons/arrow-up.js":
/*!***************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/arrow-up.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArrowUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst ArrowUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowUp\", [\n  [\"path\", { d: \"m5 12 7-7 7 7\", key: \"hav0vg\" }],\n  [\"path\", { d: \"M12 19V5\", key: \"x0mq9r\" }]\n]);\n\n\n//# sourceMappingURL=arrow-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9hcnJvdy11cC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGdCQUFnQixnRUFBZ0I7QUFDaEMsYUFBYSxtQ0FBbUM7QUFDaEQsYUFBYSw4QkFBOEI7QUFDM0M7O0FBRThCO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2Fycm93LXVwLmpzPzdiNWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBBcnJvd1VwID0gY3JlYXRlTHVjaWRlSWNvbihcIkFycm93VXBcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJtNSAxMiA3LTcgNyA3XCIsIGtleTogXCJoYXYwdmdcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEyIDE5VjVcIiwga2V5OiBcIngwbXE5clwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQXJyb3dVcCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcnJvdy11cC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../node_modules/lucide-react/dist/esm/icons/arrow-up.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=ArrowDown,ArrowUp!=!../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDown,ArrowUp!=!../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDown: function() { return /* reexport safe */ _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   ArrowUp: function() { return /* reexport safe */ _icons_arrow_up_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_arrow_down_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/arrow-down.js */ \"../node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _icons_arrow_up_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/arrow-up.js */ \"../node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0Rvd24sQXJyb3dVcCE9IS4uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzREIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9hYmYyIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd0Rvd24gfSBmcm9tIFwiLi9pY29ucy9hcnJvdy1kb3duLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dVcCB9IGZyb20gXCIuL2ljb25zL2Fycm93LXVwLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowDown,ArrowUp!=!../node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./components/TeleShopDashboard.tsx":
/*!******************************************!*\
  !*** ./components/TeleShopDashboard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDown_ArrowUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDown,ArrowUp!=!lucide-react */ \"__barrel_optimize__?names=ArrowDown,ArrowUp!=!../node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\nconst TeleShopDashboard = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalOrders: 0,\n        totalRevenue: 0,\n        totalCustomers: 0,\n        conversionRate: 0,\n        orderGrowth: 0,\n        revenueGrowth: 0,\n        customerGrowth: 0,\n        conversionGrowth: 0\n    });\n    const [topProducts, setTopProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [revenueData, setRevenueData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Simulate API calls with mock data for now\n            setTimeout(()=>{\n                setStats({\n                    totalOrders: 156,\n                    totalRevenue: 12426,\n                    totalCustomers: 834,\n                    conversionRate: 3.6,\n                    orderGrowth: 12.5,\n                    revenueGrowth: 8.2,\n                    customerGrowth: 4.6,\n                    conversionGrowth: -1.2\n                });\n                setTopProducts([\n                    {\n                        id: \"1\",\n                        name: \"Premium Headphones\",\n                        revenue: 4800,\n                        sales: 48\n                    },\n                    {\n                        id: \"2\",\n                        name: \"Wireless Earbuds\",\n                        revenue: 3360,\n                        sales: 42\n                    },\n                    {\n                        id: \"3\",\n                        name: \"Smartphone Case\",\n                        revenue: 720,\n                        sales: 36\n                    },\n                    {\n                        id: \"4\",\n                        name: \"Smart Watch\",\n                        revenue: 3600,\n                        sales: 30\n                    },\n                    {\n                        id: \"5\",\n                        name: \"Power Bank 10000mAh\",\n                        revenue: 960,\n                        sales: 24\n                    }\n                ]);\n                setRevenueData([\n                    {\n                        day: \"Mon\",\n                        revenue: 1000,\n                        profit: 800\n                    },\n                    {\n                        day: \"Tue\",\n                        revenue: 1500,\n                        profit: 1200\n                    },\n                    {\n                        day: \"Wed\",\n                        revenue: 2000,\n                        profit: 1600\n                    },\n                    {\n                        day: \"Thu\",\n                        revenue: 2500,\n                        profit: 1800\n                    },\n                    {\n                        day: \"Fri\",\n                        revenue: 3000,\n                        profit: 2200\n                    },\n                    {\n                        day: \"Sat\",\n                        revenue: 4500,\n                        profit: 2500\n                    },\n                    {\n                        day: \"Sun\",\n                        revenue: 3500,\n                        profit: 2000\n                    }\n                ]);\n                setLoading(false);\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const StatCard = (param)=>{\n        let { title, value, growth, icon, prefix = \"\", suffix = \"\", iconBg = \"bg-blue-500\" } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"stat-card group\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium\",\n                                    style: {\n                                        color: \"var(--text-secondary)\"\n                                    },\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-lg \".concat(iconBg),\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold mb-3\",\n                            style: {\n                                color: \"var(--text-primary)\"\n                            },\n                            children: [\n                                prefix,\n                                typeof value === \"number\" ? value.toLocaleString() : value,\n                                suffix\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                growth >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ArrowUp, {\n                                    className: \"w-4 h-4 text-green-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDown_ArrowUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ArrowDown, {\n                                    className: \"w-4 h-4 text-red-400 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium \".concat(growth >= 0 ? \"text-green-400\" : \"text-red-400\"),\n                                    children: [\n                                        growth >= 0 ? \"+\" : \"\",\n                                        growth,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm ml-2\",\n                                    style: {\n                                        color: \"var(--text-tertiary)\"\n                                    },\n                                    children: \"from last period\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 110,\n            columnNumber: 5\n        }, undefined);\n    };\n    const SimpleChart = (param)=>{\n        let { data } = param;\n        const maxValue = Math.max(...data.map((d)=>Math.max(d.revenue, d.profit)));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-64 flex items-end justify-between px-4 py-4\",\n            children: data.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center flex-1 mx-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-end h-48 w-full relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-blue-500 rounded-t-sm mr-1\",\n                                    style: {\n                                        height: \"\".concat(item.revenue / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 bg-green-500 rounded-t-sm ml-1 absolute bottom-0\",\n                                    style: {\n                                        height: \"\".concat(item.profit / maxValue * 100, \"%\"),\n                                        minHeight: \"4px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs mt-2\",\n                            style: {\n                                color: \"var(--text-secondary)\"\n                            },\n                            children: item.day\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, item.day, true, {\n                    fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 rounded-lg\",\n                                style: {\n                                    backgroundColor: \"var(--bg-tertiary)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Orders\",\n                        value: stats.totalOrders,\n                        growth: stats.orderGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShoppingBagIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Total Revenue\",\n                        value: stats.totalRevenue,\n                        growth: stats.revenueGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyDollarIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 17\n                        }, void 0),\n                        prefix: \"$\",\n                        iconBg: \"bg-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Active Customers\",\n                        value: stats.totalCustomers,\n                        growth: stats.customerGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 17\n                        }, void 0),\n                        iconBg: \"bg-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                        title: \"Conversion Rate\",\n                        value: stats.conversionRate,\n                        growth: stats.conversionGrowth,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrendingUpIcon, {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 17\n                        }, void 0),\n                        suffix: \"%\",\n                        iconBg: \"bg-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        style: {\n                                            color: \"var(--text-primary)\"\n                                        },\n                                        children: \"Revenue\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Profit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleChart, {\n                                data: revenueData\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-6\",\n                                style: {\n                                    color: \"var(--text-primary)\"\n                                },\n                                children: \"Top Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: topProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 rounded-lg hover:bg-gray-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium w-6 h-6 flex items-center justify-center rounded-full bg-blue-500 text-white mr-3\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                style: {\n                                                                    color: \"var(--text-primary)\"\n                                                                },\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                style: {\n                                                                    color: \"var(--text-secondary)\"\n                                                                },\n                                                                children: [\n                                                                    product.sales,\n                                                                    \" sold\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        style: {\n                                                            color: \"var(--text-primary)\"\n                                                        },\n                                                        children: [\n                                                            \"$\",\n                                                            product.revenue\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        style: {\n                                                            color: \"var(--text-secondary)\"\n                                                        },\n                                                        children: \"Revenue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/us.sitesucker.mac.sitesucker/TG/admin-panel/components/TeleShopDashboard.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TeleShopDashboard, \"/MjSXFkUfTmQBKMxalkGXohaCL8=\");\n_c = TeleShopDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeleShopDashboard);\nvar _c;\n$RefreshReg$(_c, \"TeleShopDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/TeleShopDashboard.tsx\n"));

/***/ })

});