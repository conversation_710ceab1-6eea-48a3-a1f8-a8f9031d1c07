{"c": ["pages/_app", "pages/index", "webpack"], "r": ["pages/orders", "pages/products"], "m": ["../node_modules/lucide-react/dist/esm/icons/clock.js", "../node_modules/lucide-react/dist/esm/icons/eye.js", "../node_modules/lucide-react/dist/esm/icons/filter.js", "../node_modules/lucide-react/dist/esm/icons/x-circle.js", "../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fgiopff%2FDownloads%2Fus.sitesucker.mac.sitesucker%2FTG%2Fadmin-panel%2Fpages%2Forders.tsx&page=%2Forders!", "../node_modules/next/dist/compiled/crypto-browserify/index.js", "../node_modules/next/dist/compiled/events/events.js", "../node_modules/next/dist/compiled/stream-browserify/index.js", "../node_modules/next/dist/compiled/util/util.js", "../node_modules/next/dist/compiled/vm-browserify/index.js", "../node_modules/string_decoder/lib/string_decoder.js", "../node_modules/string_decoder/node_modules/safe-buffer/index.js", "../shared/dist/index.js", "../shared/dist/types/index.js", "../shared/dist/utils/index.js", "./pages/orders.tsx", "__barrel_optimize__?names=CheckCircle,Clock,Eye,Filter,Package,Search,XCircle!=!../node_modules/lucide-react/dist/esm/lucide-react.js", "../node_modules/lucide-react/dist/esm/icons/eye-off.js", "../node_modules/lucide-react/dist/esm/icons/file.js", "../node_modules/lucide-react/dist/esm/icons/image.js", "../node_modules/lucide-react/dist/esm/icons/pen-square.js", "../node_modules/lucide-react/dist/esm/icons/plus.js", "../node_modules/lucide-react/dist/esm/icons/trash-2.js", "../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fgiopff%2FDownloads%2Fus.sitesucker.mac.sitesucker%2FTG%2Fadmin-panel%2Fpages%2Fproducts.tsx&page=%2Fproducts!", "../node_modules/react-hook-form/dist/index.esm.mjs", "./components/ProductModal.tsx", "./pages/products.tsx", "__barrel_optimize__?names=Edit,Eye,EyeOff,Plus,Search,Trash2!=!../node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=File,Image,X!=!../node_modules/lucide-react/dist/esm/lucide-react.js"]}