@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme System */
:root {
  /* Light theme colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  /* Brand colors */
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #a5b4fc;
  --primary-bg: #eef2ff;

  /* Status colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

.dark {
  /* Dark theme colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: #334155;
  --border-light: #475569;
}

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    @apply hover:shadow-lg focus:ring-indigo-500;
  }

  .btn-secondary:hover {
    background-color: var(--bg-tertiary);
  }

  .btn-ghost {
    color: var(--text-secondary);
    @apply hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-indigo-500;
  }

  .btn-ghost:hover {
    color: var(--text-primary);
  }

  /* Card Components */
  .card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    @apply rounded-2xl p-6 shadow-lg transition-all duration-200;
  }

  .card-hover:hover {
    @apply shadow-xl transform -translate-y-1;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-indigo-500 to-purple-600 text-white border-0;
  }

  /* Input Components */
  .input {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    @apply block w-full px-4 py-3 rounded-xl placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200;
  }

  /* Utility Classes */
  .glass {
    @apply backdrop-blur-xl bg-white/10 border border-white/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Table Styles */
  .table-modern {
    @apply w-full border-collapse;
  }

  .table-modern th {
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
    @apply text-left py-4 px-4 text-sm font-semibold;
  }

  .table-modern td {
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    @apply py-4 px-4 text-sm;
  }

  .table-modern tr:hover {
    background-color: var(--bg-tertiary);
  }
}
