import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { toast } from 'react-hot-toast'
import Layout from '../components/Layout'
import { apiService } from '../services/api'
import { Product, formatCurrency, formatFileSize, formatDate } from '@ecommerce/shared'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Upload, 
  Eye,
  EyeOff,
  Filter
} from 'lucide-react'
import LoadingSpinner from '../components/LoadingSpinner'
import ProductModal from '../components/ProductModal'

export default function Products() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [category, setCategory] = useState('')
  const [showInactive, setShowInactive] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)

  const queryClient = useQueryClient()

  const { data: productsData, isLoading } = useQuery(
    ['products', page, search, category, showInactive],
    () => apiService.getProducts({
      page,
      limit: 10,
      search: search || undefined,
      category: category || undefined,
      isActive: showInactive ? undefined : true
    }),
    {
      keepPreviousData: true
    }
  )

  const deleteProductMutation = useMutation(
    (id: string) => apiService.deleteProduct(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('products')
        toast.success('Product deleted successfully')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to delete product')
      }
    }
  )

  const toggleProductStatusMutation = useMutation(
    ({ id, isActive }: { id: string; isActive: boolean }) =>
      apiService.updateProduct(id, { isActive }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('products')
        toast.success('Product status updated')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update product')
      }
    }
  )

  const handleCreateProduct = () => {
    setSelectedProduct(null)
    setIsCreating(true)
    setIsModalOpen(true)
  }

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setIsCreating(false)
    setIsModalOpen(true)
  }

  const handleDeleteProduct = async (product: Product) => {
    if (window.confirm(`Are you sure you want to delete "${product.name}"?`)) {
      deleteProductMutation.mutate(product.id)
    }
  }

  const handleToggleStatus = (product: Product) => {
    toggleProductStatusMutation.mutate({
      id: product.id,
      isActive: !product.isActive
    })
  }

  const products = productsData?.data?.products || []
  const pagination = productsData?.data?.pagination

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Products</h1>
            <p className="text-gray-600">Manage your digital products</p>
          </div>
          <button
            onClick={handleCreateProduct}
            className="btn btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Product</span>
          </button>
        </div>

        {/* Filters */}
        <div className="card">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="input pl-10"
              />
            </div>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="input"
            >
              <option value="">All Categories</option>
              <option value="Software">Software</option>
              <option value="E-books">E-books</option>
              <option value="Templates">Templates</option>
              <option value="Courses">Courses</option>
            </select>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showInactive"
                checked={showInactive}
                onChange={(e) => setShowInactive(e.target.checked)}
                className="rounded border-gray-300"
              />
              <label htmlFor="showInactive" className="text-sm text-gray-700">
                Show inactive products
              </label>
            </div>
          </div>
        </div>

        {/* Products Table */}
        <div className="card">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Category</th>
                      <th>Price</th>
                      <th>File Size</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product: Product) => (
                      <tr key={product.id}>
                        <td>
                          <div className="flex items-center space-x-3">
                            {product.thumbnailUrl && (
                              <img
                                src={`${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '')}${product.thumbnailUrl}`}
                                alt={product.name}
                                className="h-10 w-10 rounded object-cover"
                              />
                            )}
                            <div>
                              <div className="font-medium">{product.name}</div>
                              <div className="text-sm text-gray-500">
                                {product.description.substring(0, 50)}...
                              </div>
                            </div>
                          </div>
                        </td>
                        <td>{product.category}</td>
                        <td>{formatCurrency(product.price, product.currency)}</td>
                        <td>{formatFileSize(product.fileSize)}</td>
                        <td>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              product.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {product.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td>{formatDate(product.createdAt)}</td>
                        <td>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleEditProduct(product)}
                              className="p-1 text-blue-600 hover:text-blue-800"
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleToggleStatus(product)}
                              className="p-1 text-gray-600 hover:text-gray-800"
                              title={product.isActive ? 'Deactivate' : 'Activate'}
                            >
                              {product.isActive ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                            <button
                              onClick={() => handleDeleteProduct(product)}
                              className="p-1 text-red-600 hover:text-red-800"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-6">
                  <div className="text-sm text-gray-700">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setPage(page - 1)}
                      disabled={!pagination.hasPrev}
                      className="btn btn-secondary disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setPage(page + 1)}
                      disabled={!pagination.hasNext}
                      className="btn btn-secondary disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Product Modal */}
      {isModalOpen && (
        <ProductModal
          product={selectedProduct}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          isCreating={isCreating}
        />
      )}
    </Layout>
  )
}
