import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { toast } from 'react-hot-toast'
import Layout from '@/components/Layout'
import { apiService } from '@/services/api'
import { Order, OrderStatus, formatCurrency, formatDate } from '@ecommerce/shared'
import { 
  Search, 
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Package
} from 'lucide-react'
import LoadingSpinner from '@/components/LoadingSpinner'

export default function Orders() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showOrderModal, setShowOrderModal] = useState(false)

  const queryClient = useQueryClient()

  const { data: ordersData, isLoading } = useQuery(
    ['orders', page, search, statusFilter],
    () => apiService.getOrders({
      page,
      limit: 10,
      search: search || undefined,
      status: statusFilter || undefined
    }),
    {
      keepPreviousData: true
    }
  )

  const updateOrderStatusMutation = useMutation(
    ({ id, status, transactionHash }: { id: string; status: string; transactionHash?: string }) =>
      apiService.updateOrderStatus(id, status, transactionHash),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('orders')
        toast.success('Order status updated successfully')
        setShowOrderModal(false)
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update order status')
      }
    }
  )

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order)
    setShowOrderModal(true)
  }

  const handleUpdateStatus = (status: OrderStatus, transactionHash?: string) => {
    if (selectedOrder) {
      updateOrderStatusMutation.mutate({
        id: selectedOrder.id,
        status,
        transactionHash
      })
    }
  }

  const orders = ordersData?.data?.orders || []
  const pagination = ordersData?.data?.pagination

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600">Manage customer orders and payments</p>
        </div>

        {/* Filters */}
        <div className="card">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search orders..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="input pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input"
            >
              <option value="">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="PAID">Paid</option>
              <option value="DELIVERED">Delivered</option>
              <option value="CANCELLED">Cancelled</option>
              <option value="REFUNDED">Refunded</option>
            </select>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                {orders.length} orders found
              </span>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="card">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Order ID</th>
                      <th>Customer</th>
                      <th>Items</th>
                      <th>Total</th>
                      <th>Payment Method</th>
                      <th>Status</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order: Order) => (
                      <tr key={order.id}>
                        <td className="font-mono text-sm">
                          #{order.id.substring(0, 8)}
                        </td>
                        <td>
                          <div>
                            <div className="font-medium">{order.user.email}</div>
                            {order.user.username && (
                              <div className="text-sm text-gray-500">@{order.user.username}</div>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="text-sm">
                            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="font-medium">
                              {formatCurrency(order.totalAmount, order.currency)}
                            </div>
                            {order.discountAmount > 0 && (
                              <div className="text-sm text-green-600">
                                -{formatCurrency(order.discountAmount, order.currency)} discount
                              </div>
                            )}
                          </div>
                        </td>
                        <td>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {order.paymentMethod}
                          </span>
                        </td>
                        <td>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
                            {getStatusIcon(order.status)}
                            {order.status}
                          </span>
                        </td>
                        <td>{formatDate(order.createdAt)}</td>
                        <td>
                          <button
                            onClick={() => handleViewOrder(order)}
                            className="p-1 text-blue-600 hover:text-blue-800"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-6">
                  <div className="text-sm text-gray-700">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setPage(page - 1)}
                      disabled={!pagination.hasPrev}
                      className="btn btn-secondary disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setPage(page + 1)}
                      disabled={!pagination.hasNext}
                      className="btn btn-secondary disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => setShowOrderModal(false)}
          onUpdateStatus={handleUpdateStatus}
          isUpdating={updateOrderStatusMutation.isLoading}
        />
      )}
    </Layout>
  )
}

interface OrderDetailsModalProps {
  order: Order
  onClose: () => void
  onUpdateStatus: (status: OrderStatus, transactionHash?: string) => void
  isUpdating: boolean
}

function OrderDetailsModal({ order, onClose, onUpdateStatus, isUpdating }: OrderDetailsModalProps) {
  const [newStatus, setNewStatus] = useState<OrderStatus>(order.status)
  const [transactionHash, setTransactionHash] = useState(order.transactionHash || '')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onUpdateStatus(newStatus, transactionHash || undefined)
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose} />

        <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              Order Details - #{order.id.substring(0, 8)}
            </h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <XCircle className="h-6 w-6" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Order Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Customer</label>
                <p className="mt-1 text-sm text-gray-900">{order.user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Order Date</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(order.createdAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                <p className="mt-1 text-sm text-gray-900">{order.paymentMethod}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                <p className="mt-1 text-sm text-gray-900">
                  {formatCurrency(order.totalAmount, order.currency)}
                </p>
              </div>
            </div>

            {/* Payment Address */}
            {order.paymentAddress && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Address</label>
                <p className="mt-1 text-sm text-gray-900 font-mono break-all">
                  {order.paymentAddress}
                </p>
              </div>
            )}

            {/* Order Items */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Order Items</label>
              <div className="space-y-2">
                {order.items.map((item: any) => (
                  <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{item.product.name}</p>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                    </div>
                    <p className="font-medium">
                      {formatCurrency(item.price * item.quantity, order.currency)}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Update Status Form */}
            <form onSubmit={handleSubmit} className="space-y-4 border-t pt-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <select
                    value={newStatus}
                    onChange={(e) => setNewStatus(e.target.value as OrderStatus)}
                    className="input"
                  >
                    <option value="PENDING">Pending</option>
                    <option value="PAID">Paid</option>
                    <option value="DELIVERED">Delivered</option>
                    <option value="CANCELLED">Cancelled</option>
                    <option value="REFUNDED">Refunded</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Transaction Hash (optional)
                  </label>
                  <input
                    type="text"
                    value={transactionHash}
                    onChange={(e) => setTransactionHash(e.target.value)}
                    className="input"
                    placeholder="Enter transaction hash"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button type="button" onClick={onClose} className="btn btn-secondary">
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isUpdating}
                  className="btn btn-primary flex items-center space-x-2"
                >
                  {isUpdating && <LoadingSpinner size="sm" />}
                  <span>Update Status</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

function getStatusBadgeColor(status: OrderStatus) {
  switch (status) {
    case 'DELIVERED': return 'bg-green-100 text-green-800'
    case 'PAID': return 'bg-blue-100 text-blue-800'
    case 'PENDING': return 'bg-yellow-100 text-yellow-800'
    case 'CANCELLED': return 'bg-red-100 text-red-800'
    case 'REFUNDED': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function getStatusIcon(status: OrderStatus) {
  switch (status) {
    case 'DELIVERED': return <Package className="h-3 w-3 mr-1" />
    case 'PAID': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PENDING': return <Clock className="h-3 w-3 mr-1" />
    case 'CANCELLED': return <XCircle className="h-3 w-3 mr-1" />
    case 'REFUNDED': return <XCircle className="h-3 w-3 mr-1" />
    default: return null
  }
}
