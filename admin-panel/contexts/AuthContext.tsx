import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User } from '@ecommerce/shared'
import { apiService } from '../services/api'

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('accessToken')
      if (token) {
        apiService.setAuthToken(token)
        const response = await apiService.getCurrentUser()
        if (response.success && response.data) {
          setUser(response.data.user)
        } else {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    console.log('Login attempt:', { email, password: '***' })

    try {
      const response = await apiService.login(email, password)
      console.log('Login response:', response)

      if (response.success && response.data) {
        const { user, tokens } = response.data
        console.log('Login successful, user:', user)

        // Check if user is admin
        if (user.role !== 'ADMIN') {
          throw new Error('Access denied. Admin privileges required.')
        }

        localStorage.setItem('accessToken', tokens.accessToken)
        localStorage.setItem('refreshToken', tokens.refreshToken)
        apiService.setAuthToken(tokens.accessToken)
        setUser(user)
        console.log('User set successfully')
      } else {
        console.error('Login failed:', response)
        throw new Error(response.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  const logout = () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    apiService.removeAuthToken()
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
