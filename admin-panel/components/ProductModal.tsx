import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useMutation, useQueryClient } from 'react-query'
import { toast } from 'react-hot-toast'
import { X, Upload, Image, File } from 'lucide-react'
import { apiService } from '@/services/api'
import { Product } from '@ecommerce/shared'
import LoadingSpinner from './LoadingSpinner'

interface ProductModalProps {
  product: Product | null
  isOpen: boolean
  onClose: () => void
  isCreating: boolean
}

interface ProductForm {
  name: string
  description: string
  price: number
  currency: string
  category: string
  tags: string
  downloadLimit: number
  isActive: boolean
}

export default function ProductModal({ product, isOpen, onClose, isCreating }: ProductModalProps) {
  const [productFile, setProductFile] = useState<File | null>(null)
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null)
  const [previewFile, setPreviewFile] = useState<File | null>(null)
  const [currentProductId, setCurrentProductId] = useState<string | null>(null)

  const queryClient = useQueryClient()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<ProductForm>({
    defaultValues: {
      currency: 'USD',
      downloadLimit: 3,
      isActive: true
    }
  })

  useEffect(() => {
    if (product) {
      reset({
        name: product.name,
        description: product.description,
        price: product.price,
        currency: product.currency,
        category: product.category,
        tags: product.tags.join(', '),
        downloadLimit: product.downloadLimit,
        isActive: product.isActive
      })
      setCurrentProductId(product.id)
    } else {
      reset({
        currency: 'USD',
        downloadLimit: 3,
        isActive: true
      })
      setCurrentProductId(null)
    }
  }, [product, reset])

  const createProductMutation = useMutation(
    (data: any) => apiService.createProduct(data),
    {
      onSuccess: (response) => {
        if (response.success && response.data) {
          setCurrentProductId(response.data.product.id)
          toast.success('Product created successfully')
          queryClient.invalidateQueries('products')
        }
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to create product')
      }
    }
  )

  const updateProductMutation = useMutation(
    ({ id, data }: { id: string; data: any }) => apiService.updateProduct(id, data),
    {
      onSuccess: () => {
        toast.success('Product updated successfully')
        queryClient.invalidateQueries('products')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update product')
      }
    }
  )

  const uploadFileMutation = useMutation(
    ({ productId, file, type }: { productId: string; file: File; type: 'product' | 'thumbnail' | 'preview' }) => {
      switch (type) {
        case 'product':
          return apiService.uploadProductFile(productId, file)
        case 'thumbnail':
          return apiService.uploadThumbnail(productId, file)
        case 'preview':
          return apiService.uploadPreview(productId, file)
        default:
          throw new Error('Invalid file type')
      }
    },
    {
      onSuccess: (response, variables) => {
        toast.success(`${variables.type} uploaded successfully`)
        queryClient.invalidateQueries('products')
      },
      onError: (error: any, variables) => {
        toast.error(`Failed to upload ${variables.type}`)
      }
    }
  )

  const onSubmit = async (data: ProductForm) => {
    try {
      const formData = {
        ...data,
        tags: data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
      }

      if (isCreating) {
        const result = await createProductMutation.mutateAsync(formData)
        if (result.success && result.data) {
          const productId = result.data.product.id
          
          // Upload files if provided
          if (productFile) {
            await uploadFileMutation.mutateAsync({ productId, file: productFile, type: 'product' })
          }
          if (thumbnailFile) {
            await uploadFileMutation.mutateAsync({ productId, file: thumbnailFile, type: 'thumbnail' })
          }
          if (previewFile) {
            await uploadFileMutation.mutateAsync({ productId, file: previewFile, type: 'preview' })
          }
        }
      } else if (product) {
        await updateProductMutation.mutateAsync({ id: product.id, data: formData })
        
        // Upload files if provided
        if (productFile) {
          await uploadFileMutation.mutateAsync({ productId: product.id, file: productFile, type: 'product' })
        }
        if (thumbnailFile) {
          await uploadFileMutation.mutateAsync({ productId: product.id, file: thumbnailFile, type: 'thumbnail' })
        }
        if (previewFile) {
          await uploadFileMutation.mutateAsync({ productId: product.id, file: previewFile, type: 'preview' })
        }
      }

      onClose()
    } catch (error) {
      // Error handling is done in mutations
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose} />

        <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">
              {isCreating ? 'Create Product' : 'Edit Product'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name *
                </label>
                <input
                  {...register('name', { required: 'Product name is required' })}
                  className="input"
                  placeholder="Enter product name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category *
                </label>
                <select
                  {...register('category', { required: 'Category is required' })}
                  className="input"
                >
                  <option value="">Select category</option>
                  <option value="Software">Software</option>
                  <option value="E-books">E-books</option>
                  <option value="Templates">Templates</option>
                  <option value="Courses">Courses</option>
                  <option value="Graphics">Graphics</option>
                  <option value="Audio">Audio</option>
                  <option value="Video">Video</option>
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'Description is required' })}
                rows={4}
                className="input"
                placeholder="Enter product description"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price *
                </label>
                <input
                  {...register('price', { 
                    required: 'Price is required',
                    min: { value: 0.01, message: 'Price must be greater than 0' }
                  })}
                  type="number"
                  step="0.01"
                  className="input"
                  placeholder="0.00"
                />
                {errors.price && (
                  <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <select {...register('currency')} className="input">
                  <option value="USD">USD</option>
                  <option value="BITCOIN">Bitcoin</option>
                  <option value="ETHEREUM">Ethereum</option>
                  <option value="USDT">USDT</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Download Limit
                </label>
                <input
                  {...register('downloadLimit', { min: 1 })}
                  type="number"
                  className="input"
                  placeholder="3"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma separated)
              </label>
              <input
                {...register('tags')}
                className="input"
                placeholder="tag1, tag2, tag3"
              />
            </div>

            {/* File Uploads */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900">File Uploads</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FileUpload
                  label="Product File"
                  accept=".pdf,.zip,.rar,.7z,.exe,.dmg,.pkg"
                  file={productFile}
                  onFileChange={setProductFile}
                  icon={File}
                />
                <FileUpload
                  label="Thumbnail"
                  accept="image/*"
                  file={thumbnailFile}
                  onFileChange={setThumbnailFile}
                  icon={Image}
                />
                <FileUpload
                  label="Preview Image"
                  accept="image/*"
                  file={previewFile}
                  onFileChange={setPreviewFile}
                  icon={Image}
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                {...register('isActive')}
                type="checkbox"
                className="rounded border-gray-300"
              />
              <label className="ml-2 text-sm text-gray-700">
                Active (visible to customers)
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn btn-primary flex items-center space-x-2"
              >
                {isSubmitting && <LoadingSpinner size="sm" />}
                <span>{isCreating ? 'Create Product' : 'Update Product'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

interface FileUploadProps {
  label: string
  accept: string
  file: File | null
  onFileChange: (file: File | null) => void
  icon: React.ElementType
}

function FileUpload({ label, accept, file, onFileChange, icon: Icon }: FileUploadProps) {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
        <Icon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <input
          type="file"
          accept={accept}
          onChange={(e) => onFileChange(e.target.files?.[0] || null)}
          className="hidden"
          id={`file-${label}`}
        />
        <label
          htmlFor={`file-${label}`}
          className="cursor-pointer text-sm text-blue-600 hover:text-blue-800"
        >
          {file ? file.name : 'Choose file'}
        </label>
        {file && (
          <button
            type="button"
            onClick={() => onFileChange(null)}
            className="block mt-1 text-xs text-red-600 hover:text-red-800 mx-auto"
          >
            Remove
          </button>
        )}
      </div>
    </div>
  )
}
