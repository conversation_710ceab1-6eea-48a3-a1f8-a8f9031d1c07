import React, { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  FunnelIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline'
import { apiService } from '../services/api'

interface Product {
  id: string
  name: string
  price: number
  category: string
  inventory: number
  status: 'Available' | 'Unavailable'
  image?: string
}

const TeleShopProducts: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedStatus, setSelectedStatus] = useState('All Status')
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddModal, setShowAddModal] = useState(false)
  const itemsPerPage = 6

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      // Mock data for now
      setTimeout(() => {
        setProducts([
          { id: '1', name: 'Wireless Headphones', price: 89.99, category: 'Electronics', inventory: 45, status: 'Available' },
          { id: '2', name: 'Smart Watch', price: 199.99, category: 'Electronics', inventory: 28, status: 'Available' },
          { id: '3', name: 'Bluetooth Speaker', price: 79.99, category: 'Electronics', inventory: 60, status: 'Available' },
          { id: '4', name: 'Phone Case', price: 19.99, category: 'Accessories', inventory: 120, status: 'Available' },
          { id: '5', name: 'Wireless Earbuds', price: 129.99, category: 'Electronics', inventory: 35, status: 'Available' },
          { id: '6', name: 'Power Bank', price: 49.99, category: 'Electronics', inventory: 0, status: 'Unavailable' },
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching products:', error)
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All Categories' || product.category === selectedCategory
    const matchesStatus = selectedStatus === 'All Status' || product.status === selectedStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage)

  const StatusBadge: React.FC<{ status: string }> = ({ status }) => (
    <span className={`badge ${status === 'Available' ? 'badge-success' : 'badge-error'}`}>
      {status}
    </span>
  )

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Product Management
          </h1>
          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
            Manage your product inventory and listings
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="btn btn-secondary">
            <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
            Export
          </button>
          <button className="btn btn-secondary">
            <ArrowUpTrayIcon className="w-4 h-4 mr-2" />
            Import
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="select"
            >
              <option>All Categories</option>
              <option>Electronics</option>
              <option>Accessories</option>
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select"
            >
              <option>All Status</option>
              <option>Available</option>
              <option>Unavailable</option>
            </select>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="card p-0 overflow-hidden">
        <table className="table-teleshop">
          <thead>
            <tr>
              <th>Product</th>
              <th>Price</th>
              <th>Category</th>
              <th>Inventory</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedProducts.map((product) => (
              <tr key={product.id}>
                <td>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gray-600 rounded-lg mr-3 flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {product.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                        {product.name}
                      </p>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        ID: {product.id}
                      </p>
                    </div>
                  </div>
                </td>
                <td>
                  <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                    ${product.price}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-secondary)' }}>
                    {product.category}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-primary)' }}>
                    {product.inventory}
                  </span>
                </td>
                <td>
                  <StatusBadge status={product.status} />
                </td>
                <td>
                  <button className="p-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <EllipsisVerticalIcon className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredProducts.length)} of {filteredProducts.length} entries
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Previous
          </button>
          <span className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
            {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default TeleShopProducts
