import React, { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  EllipsisVerticalIcon,
  UserPlusIcon
} from '@heroicons/react/24/outline'
import { apiService } from '../services/api'

interface Customer {
  id: string
  name: string
  username: string
  email: string
  phone: string
  orders: number
  totalSpent: number
  lastActive: string
  status: 'Active' | 'Inactive'
}

const TeleShopCustomers: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('All Status')
  const [sortBy, setSortBy] = useState('Sort By')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setLoading(true)
      // Mock data for now
      setTimeout(() => {
        setCustomers([
          {
            id: '1',
            name: '<PERSON>',
            username: '@johndoe',
            email: '<EMAIL>',
            phone: '+****************',
            orders: 5,
            totalSpent: 299.95,
            lastActive: '5/10/2025, 3:30:00 PM',
            status: 'Active'
          },
          {
            id: '2',
            name: 'Jane Smith',
            username: '@janesmith',
            email: '<EMAIL>',
            phone: '+****************',
            orders: 3,
            totalSpent: 159.97,
            lastActive: '5/9/2025, 6:45:00 PM',
            status: 'Active'
          },
          {
            id: '3',
            name: 'Robert Johnson',
            username: '@robertj',
            email: '<EMAIL>',
            phone: '+****************',
            orders: 8,
            totalSpent: 725.60,
            lastActive: '5/10/2025, 12:15:00 PM',
            status: 'Active'
          },
          {
            id: '4',
            name: 'Sarah Wilson',
            username: '@sarahw',
            email: '<EMAIL>',
            phone: '+****************',
            orders: 2,
            totalSpent: 89.98,
            lastActive: '5/8/2025, 7:20:00 PM',
            status: 'Active'
          },
          {
            id: '5',
            name: 'Michael Brown',
            username: '@michaelb',
            email: '<EMAIL>',
            phone: '+****************',
            orders: 0,
            totalSpent: 0.00,
            lastActive: '4/20/2025, 1:30:00 PM',
            status: 'Inactive'
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching customers:', error)
      setLoading(false)
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'All Status' || customer.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCustomers = filteredCustomers.slice(startIndex, startIndex + itemsPerPage)

  const StatusBadge: React.FC<{ status: string }> = ({ status }) => (
    <span className={`badge ${status === 'Active' ? 'badge-success' : 'badge-error'}`}>
      {status}
    </span>
  )

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-64 mb-6"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
            Customer Management
          </h1>
          <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
            Manage your customer base and relationships
          </p>
        </div>
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <button className="btn btn-secondary">
            <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
            Export
          </button>
          <button className="btn btn-primary">
            <UserPlusIcon className="w-4 h-4 mr-2" />
            Add Customer
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{ color: 'var(--text-secondary)' }} />
            <input
              type="text"
              placeholder="Search by name, username, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10 pr-4 py-2 w-full"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select"
            >
              <option>All Status</option>
              <option>Active</option>
              <option>Inactive</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="select"
            >
              <option>Sort By</option>
              <option>Name</option>
              <option>Orders</option>
              <option>Total Spent</option>
              <option>Last Active</option>
            </select>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="card p-0 overflow-hidden">
        <table className="table-teleshop">
          <thead>
            <tr>
              <th>Customer</th>
              <th>Contact</th>
              <th>Orders</th>
              <th>Total Spent</th>
              <th>Last Active</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedCustomers.map((customer) => (
              <tr key={customer.id}>
                <td>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-500 rounded-full mr-3 flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        {customer.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                        {customer.name}
                      </p>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        {customer.username}
                      </p>
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <p className="text-sm" style={{ color: 'var(--text-primary)' }}>
                      {customer.email}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      {customer.phone}
                    </p>
                  </div>
                </td>
                <td>
                  <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                    {customer.orders}
                  </span>
                </td>
                <td>
                  <span className="font-medium" style={{ color: 'var(--text-primary)' }}>
                    ${customer.totalSpent.toFixed(2)}
                  </span>
                </td>
                <td>
                  <span style={{ color: 'var(--text-secondary)' }}>
                    {customer.lastActive}
                  </span>
                </td>
                <td>
                  <StatusBadge status={customer.status} />
                </td>
                <td>
                  <button className="p-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <EllipsisVerticalIcon className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
          Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredCustomers.length)} of {filteredCustomers.length} entries
        </p>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Previous
          </button>
          <span className="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
            {currentPage}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 rounded-lg border border-gray-600 text-sm disabled:opacity-50"
            style={{ color: 'var(--text-secondary)' }}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default TeleShopCustomers
