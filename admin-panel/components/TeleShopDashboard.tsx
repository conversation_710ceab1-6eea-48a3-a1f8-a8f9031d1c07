import React, { useState, useEffect } from 'react'
import {
  ShoppingBag,
  Users,
  DollarSign,
  BarChart3,
  ArrowUp,
  ArrowDown,
  <PERSON><PERSON>dingUp,
  Eye
} from 'lucide-react'
import { apiService } from '../services/api'

interface DashboardStats {
  totalOrders: number
  totalRevenue: number
  totalCustomers: number
  conversionRate: number
  orderGrowth: number
  revenueGrowth: number
  customerGrowth: number
  conversionGrowth: number
}

interface TopProduct {
  id: string
  name: string
  revenue: number
  sales: number
  image?: string
}

interface RevenueData {
  day: string
  revenue: number
  profit: number
}

const TeleShopDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalRevenue: 0,
    totalCustomers: 0,
    conversionRate: 0,
    orderGrowth: 0,
    revenueGrowth: 0,
    customerGrowth: 0,
    conversionGrowth: 0
  })
  const [topProducts, setTopProducts] = useState<TopProduct[]>([])
  const [revenueData, setRevenueData] = useState<RevenueData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Simulate API calls with mock data for now
      setTimeout(() => {
        setStats({
          totalOrders: 156,
          totalRevenue: 12426,
          totalCustomers: 834,
          conversionRate: 3.6,
          orderGrowth: 12.5,
          revenueGrowth: 8.2,
          customerGrowth: 4.6,
          conversionGrowth: -1.2
        })

        setTopProducts([
          { id: '1', name: 'Premium Headphones', revenue: 4800, sales: 48 },
          { id: '2', name: 'Wireless Earbuds', revenue: 3360, sales: 42 },
          { id: '3', name: 'Smartphone Case', revenue: 720, sales: 36 },
          { id: '4', name: 'Smart Watch', revenue: 3600, sales: 30 },
          { id: '5', name: 'Power Bank 10000mAh', revenue: 960, sales: 24 }
        ])

        setRevenueData([
          { day: 'Mon', revenue: 1000, profit: 800 },
          { day: 'Tue', revenue: 1500, profit: 1200 },
          { day: 'Wed', revenue: 2000, profit: 1600 },
          { day: 'Thu', revenue: 2500, profit: 1800 },
          { day: 'Fri', revenue: 3000, profit: 2200 },
          { day: 'Sat', revenue: 4500, profit: 2500 },
          { day: 'Sun', revenue: 3500, profit: 2000 }
        ])

        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setLoading(false)
    }
  }

  const StatCard: React.FC<{
    title: string
    value: string | number
    growth: number
    icon: React.ReactNode
    prefix?: string
    suffix?: string
    iconBg?: string
  }> = ({ title, value, growth, icon, prefix = '', suffix = '', iconBg = 'bg-blue-500' }) => (
    <div className="stat-card group">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
              {title}
            </p>
            <div className={`p-2 rounded-lg ${iconBg}`}>
              {icon}
            </div>
          </div>
          <p className="text-3xl font-bold mb-3" style={{ color: 'var(--text-primary)' }}>
            {prefix}{typeof value === 'number' ? value.toLocaleString() : value}{suffix}
          </p>
          <div className="flex items-center">
            {growth >= 0 ? (
              <ArrowUp className="w-4 h-4 text-green-400 mr-1" />
            ) : (
              <ArrowDown className="w-4 h-4 text-red-400 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              growth >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {growth >= 0 ? '+' : ''}{growth}%
            </span>
            <span className="text-sm ml-2" style={{ color: 'var(--text-tertiary)' }}>
              from last period
            </span>
          </div>
        </div>
      </div>
    </div>
  )

  const SimpleChart: React.FC<{ data: RevenueData[] }> = ({ data }) => {
    const maxValue = Math.max(...data.map(d => Math.max(d.revenue, d.profit)))
    
    return (
      <div className="h-64 flex items-end justify-between px-4 py-4">
        {data.map((item, index) => (
          <div key={item.day} className="flex flex-col items-center flex-1 mx-1">
            <div className="flex flex-col items-center justify-end h-48 w-full relative">
              {/* Revenue Bar */}
              <div 
                className="w-3 bg-blue-500 rounded-t-sm mr-1"
                style={{ 
                  height: `${(item.revenue / maxValue) * 100}%`,
                  minHeight: '4px'
                }}
              />
              {/* Profit Bar */}
              <div 
                className="w-3 bg-green-500 rounded-t-sm ml-1 absolute bottom-0"
                style={{ 
                  height: `${(item.profit / maxValue) * 100}%`,
                  minHeight: '4px'
                }}
              />
            </div>
            <span className="text-xs mt-2" style={{ color: 'var(--text-secondary)' }}>
              {item.day}
            </span>
          </div>
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 rounded-lg" style={{ backgroundColor: 'var(--bg-tertiary)' }}></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 rounded-lg" style={{ backgroundColor: 'var(--bg-tertiary)' }}></div>
            <div className="h-64 rounded-lg" style={{ backgroundColor: 'var(--bg-tertiary)' }}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Orders"
          value={stats.totalOrders}
          growth={stats.orderGrowth}
          icon={<ShoppingBag className="w-5 h-5 text-white" />}
          iconBg="bg-blue-500"
        />
        <StatCard
          title="Total Revenue"
          value={stats.totalRevenue}
          growth={stats.revenueGrowth}
          icon={<DollarSign className="w-5 h-5 text-white" />}
          prefix="$"
          iconBg="bg-green-500"
        />
        <StatCard
          title="Active Customers"
          value={stats.totalCustomers}
          growth={stats.customerGrowth}
          icon={<Users className="w-5 h-5 text-white" />}
          iconBg="bg-purple-500"
        />
        <StatCard
          title="Conversion Rate"
          value={stats.conversionRate}
          growth={stats.conversionGrowth}
          icon={<TrendingUp className="w-5 h-5 text-white" />}
          suffix="%"
          iconBg="bg-orange-500"
        />
      </div>

      {/* Charts and Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
              Revenue
            </h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span style={{ color: 'var(--text-secondary)' }}>Revenue</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span style={{ color: 'var(--text-secondary)' }}>Profit</span>
              </div>
            </div>
          </div>
          <SimpleChart data={revenueData} />
        </div>

        {/* Top Products */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-6" style={{ color: 'var(--text-primary)' }}>
            Top Products
          </h3>
          <div className="space-y-4">
            {topProducts.map((product, index) => (
              <div key={product.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-700 transition-colors">
                <div className="flex items-center">
                  <span className="text-sm font-medium w-6 h-6 flex items-center justify-center rounded-full bg-blue-500 text-white mr-3">
                    {index + 1}
                  </span>
                  <div>
                    <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                      {product.name}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      {product.sales} sold
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                    ${product.revenue}
                  </p>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    Revenue
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TeleShopDashboard
