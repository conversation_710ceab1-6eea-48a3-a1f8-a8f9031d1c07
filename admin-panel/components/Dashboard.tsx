import { useQuery } from 'react-query'
import { apiService } from '../services/api'
import { formatCurrency, formatDate } from '@ecommerce/shared'
import { 
  TrendingUp, 
  Users, 
  Package, 
  ShoppingCart, 
  DollarSign,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import LoadingSpinner from './LoadingSpinner'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

export default function Dashboard() {
  const { data: dashboardData, isLoading, error } = useQuery(
    'dashboard',
    () => apiService.getDashboardData(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !dashboardData?.success) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load dashboard data</p>
      </div>
    )
  }

  const dashboard = dashboardData.data.dashboard

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of your digital store performance</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Today's Revenue"
          value={formatCurrency(dashboard.today.revenue, 'USD')}
          icon={DollarSign}
          color="green"
          change="+12%"
        />
        <StatCard
          title="Today's Orders"
          value={dashboard.today.orders.toString()}
          icon={ShoppingCart}
          color="blue"
          change="+8%"
        />
        <StatCard
          title="New Customers"
          value={dashboard.today.customers.toString()}
          icon={Users}
          color="purple"
          change="+15%"
        />
        <StatCard
          title="Active Products"
          value={dashboard.overall.activeProducts.toString()}
          icon={Package}
          color="orange"
          change="0%"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Revenue Trend (Last 7 Days)</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={generateMockRevenueData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value) => [formatCurrency(value as number, 'USD'), 'Revenue']} />
                <Line type="monotone" dataKey="revenue" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Order Status Distribution */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Order Status Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={generateMockOrderStatusData()}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {generateMockOrderStatusData().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getStatusColor(entry.name)} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Monthly Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold mb-2">This Month</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Revenue:</span>
              <span className="font-semibold">{formatCurrency(dashboard.thisMonth.revenue, 'USD')}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Orders:</span>
              <span className="font-semibold">{dashboard.thisMonth.orders}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">New Customers:</span>
              <span className="font-semibold">{dashboard.thisMonth.customers}</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold mb-2">Overall Stats</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Products:</span>
              <span className="font-semibold">{dashboard.overall.totalProducts}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Active Products:</span>
              <span className="font-semibold">{dashboard.overall.activeProducts}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Pending Orders:</span>
              <span className="font-semibold text-orange-600">{dashboard.overall.pendingOrders}</span>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold mb-2">Quick Actions</h3>
          <div className="space-y-2">
            <button className="btn btn-primary w-full text-sm">Add Product</button>
            <button className="btn btn-secondary w-full text-sm">View Orders</button>
            <button className="btn btn-secondary w-full text-sm">Manage Users</button>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">Recent Orders</h3>
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
              {dashboard.recentOrders.map((order: any) => (
                <tr key={order.id}>
                  <td className="font-mono text-sm">#{order.id.substring(0, 8)}</td>
                  <td>{order.user.email}</td>
                  <td>{formatCurrency(order.totalAmount, order.currency)}</td>
                  <td>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      {order.status}
                    </span>
                  </td>
                  <td>{formatDate(order.createdAt)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

interface StatCardProps {
  title: string
  value: string
  icon: React.ElementType
  color: 'green' | 'blue' | 'purple' | 'orange'
  change: string
}

function StatCard({ title, value, icon: Icon, color, change }: StatCardProps) {
  const colorClasses = {
    green: 'bg-green-500',
    blue: 'bg-blue-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500'
  }

  return (
    <div className="card">
      <div className="flex items-center">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <div className="flex items-center">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            <span className="ml-2 text-sm text-green-600">{change}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

function generateMockRevenueData() {
  const data = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    data.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      revenue: Math.floor(Math.random() * 1000) + 500
    })
  }
  return data
}

function generateMockOrderStatusData() {
  return [
    { name: 'Delivered', value: 45 },
    { name: 'Pending', value: 25 },
    { name: 'Paid', value: 20 },
    { name: 'Cancelled', value: 10 }
  ]
}

function getStatusColor(status: string) {
  switch (status) {
    case 'Delivered': return '#10b981'
    case 'Paid': return '#3b82f6'
    case 'Pending': return '#f59e0b'
    case 'Cancelled': return '#ef4444'
    default: return '#6b7280'
  }
}

function getStatusBadgeColor(status: string) {
  switch (status) {
    case 'DELIVERED': return 'bg-green-100 text-green-800'
    case 'PAID': return 'bg-blue-100 text-blue-800'
    case 'PENDING': return 'bg-yellow-100 text-yellow-800'
    case 'CANCELLED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'DELIVERED': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PAID': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PENDING': return <Clock className="h-3 w-3 mr-1" />
    case 'CANCELLED': return <XCircle className="h-3 w-3 mr-1" />
    default: return null
  }
}
