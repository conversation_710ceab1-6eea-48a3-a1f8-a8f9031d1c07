import { useQuery } from '@tanstack/react-query'
import { apiService } from '../services/api'
import { formatCurrency, formatDate } from '@ecommerce/shared'
import {
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import LoadingSpinner from './LoadingSpinner'

export default function Dashboard() {
  const { data: dashboardData, isLoading, error } = useQuery(
    'dashboard',
    () => apiService.getDashboardData(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !dashboardData?.success) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load dashboard data</p>
      </div>
    )
  }

  const dashboard = dashboardData.data.dashboard

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome to your digital store admin panel</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Today's Revenue"
          value={formatCurrency(dashboard.today.revenue, 'USD')}
          icon={DollarSign}
          color="green"
        />
        <StatCard
          title="Today's Orders"
          value={dashboard.today.orders.toString()}
          icon={ShoppingCart}
          color="blue"
        />
        <StatCard
          title="Active Products"
          value={dashboard.overall.activeProducts.toString()}
          icon={Package}
          color="purple"
        />
        <StatCard
          title="Pending Orders"
          value={dashboard.overall.pendingOrders.toString()}
          icon={Clock}
          color="orange"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <button className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <Package className="h-4 w-4 mr-2" />
            Add Product
          </button>
          <button className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <ShoppingCart className="h-4 w-4 mr-2" />
            View Orders
          </button>
          <button className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <Users className="h-4 w-4 mr-2" />
            Manage Users
          </button>
        </div>
      </div>

      {/* Monthly Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Revenue</span>
              <span className="text-lg font-semibold text-gray-900">{formatCurrency(dashboard.thisMonth.revenue, 'USD')}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Orders</span>
              <span className="text-lg font-semibold text-gray-900">{dashboard.thisMonth.orders}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">New Customers</span>
              <span className="text-lg font-semibold text-gray-900">{dashboard.thisMonth.customers}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Store Overview</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Products</span>
              <span className="text-lg font-semibold text-gray-900">{dashboard.overall.totalProducts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Active Products</span>
              <span className="text-lg font-semibold text-green-600">{dashboard.overall.activeProducts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Pending Orders</span>
              <span className="text-lg font-semibold text-orange-600">{dashboard.overall.pendingOrders}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">Recent Orders</h3>
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Customer</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
              {dashboard.recentOrders.map((order: any) => (
                <tr key={order.id}>
                  <td className="font-mono text-sm">#{order.id.substring(0, 8)}</td>
                  <td>{order.user.email}</td>
                  <td>{formatCurrency(order.totalAmount, order.currency)}</td>
                  <td>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      {order.status}
                    </span>
                  </td>
                  <td>{formatDate(order.createdAt)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

interface StatCardProps {
  title: string
  value: string
  icon: React.ElementType
  color: 'green' | 'blue' | 'purple' | 'orange'
}

function StatCard({ title, value, icon: Icon, color }: StatCardProps) {
  const colorClasses = {
    green: 'bg-green-500',
    blue: 'bg-blue-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500'
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
        </div>
      </div>
    </div>
  )
}



function getStatusBadgeColor(status: string) {
  switch (status) {
    case 'DELIVERED': return 'bg-green-100 text-green-800'
    case 'PAID': return 'bg-blue-100 text-blue-800'
    case 'PENDING': return 'bg-yellow-100 text-yellow-800'
    case 'CANCELLED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'DELIVERED': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PAID': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PENDING': return <Clock className="h-3 w-3 mr-1" />
    case 'CANCELLED': return <XCircle className="h-3 w-3 mr-1" />
    default: return null
  }
}
