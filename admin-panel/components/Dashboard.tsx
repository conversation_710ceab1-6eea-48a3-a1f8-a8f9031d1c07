import { useQuery } from '@tanstack/react-query'
import { apiService } from '../services/api'
import { formatCurrency, formatDate } from '@ecommerce/shared'
import {
  Users,
  Package,
  ShoppingCart,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import LoadingSpinner from './LoadingSpinner'

export default function Dashboard() {
  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: ['dashboard'],
    queryFn: () => apiService.getDashboardData(),
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !dashboardData?.success) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load dashboard data</p>
      </div>
    )
  }

  const dashboard = dashboardData.data.dashboard

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center lg:text-left">
        <h1 className="text-4xl font-bold text-[var(--color-text-primary)] gradient-text">Dashboard</h1>
        <p className="text-[var(--color-text-secondary)] mt-3 text-lg">Welcome to your digital store admin panel</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Today's Revenue"
          value={formatCurrency(dashboard.today.revenue, 'USD')}
          icon={DollarSign}
          color="green"
        />
        <StatCard
          title="Today's Orders"
          value={dashboard.today.orders.toString()}
          icon={ShoppingCart}
          color="blue"
        />
        <StatCard
          title="Active Products"
          value={dashboard.overall.activeProducts.toString()}
          icon={Package}
          color="purple"
        />
        <StatCard
          title="Pending Orders"
          value={dashboard.overall.pendingOrders.toString()}
          icon={Clock}
          color="orange"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <button className="inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <Package className="h-4 w-4 mr-2" />
            Add Product
          </button>
          <button className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <ShoppingCart className="h-4 w-4 mr-2" />
            View Orders
          </button>
          <button className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <Users className="h-4 w-4 mr-2" />
            Manage Users
          </button>
        </div>
      </div>

      {/* Monthly Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card card-hover">
          <h3 className="text-xl font-bold text-[var(--color-text-primary)] mb-6 flex items-center">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-3"></div>
            This Month
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">Revenue</span>
              <span className="text-xl font-bold text-[var(--color-text-primary)]">{formatCurrency(dashboard.thisMonth.revenue, 'USD')}</span>
            </div>
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">Orders</span>
              <span className="text-xl font-bold text-[var(--color-text-primary)]">{dashboard.thisMonth.orders}</span>
            </div>
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">New Customers</span>
              <span className="text-xl font-bold text-[var(--color-text-primary)]">{dashboard.thisMonth.customers}</span>
            </div>
          </div>
        </div>

        <div className="card card-hover">
          <h3 className="text-xl font-bold text-[var(--color-text-primary)] mb-6 flex items-center">
            <div className="w-2 h-8 bg-gradient-to-b from-emerald-500 to-teal-600 rounded-full mr-3"></div>
            Store Overview
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">Total Products</span>
              <span className="text-xl font-bold text-[var(--color-text-primary)]">{dashboard.overall.totalProducts}</span>
            </div>
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">Active Products</span>
              <span className="text-xl font-bold text-emerald-600">{dashboard.overall.activeProducts}</span>
            </div>
            <div className="flex justify-between items-center p-3 rounded-xl bg-[var(--color-bg-tertiary)]">
              <span className="text-[var(--color-text-secondary)] font-medium">Pending Orders</span>
              <span className="text-xl font-bold text-orange-600">{dashboard.overall.pendingOrders}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="card">
        <h3 className="text-xl font-bold text-[var(--color-text-primary)] mb-6 flex items-center">
          <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full mr-3"></div>
          Recent Orders
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-[var(--color-border)]">
                <th className="text-left py-4 px-2 text-sm font-semibold text-[var(--color-text-secondary)]">Order ID</th>
                <th className="text-left py-4 px-2 text-sm font-semibold text-[var(--color-text-secondary)]">Customer</th>
                <th className="text-left py-4 px-2 text-sm font-semibold text-[var(--color-text-secondary)]">Amount</th>
                <th className="text-left py-4 px-2 text-sm font-semibold text-[var(--color-text-secondary)]">Status</th>
                <th className="text-left py-4 px-2 text-sm font-semibold text-[var(--color-text-secondary)]">Date</th>
              </tr>
            </thead>
            <tbody>
              {dashboard.recentOrders.map((order: any) => (
                <tr key={order.id} className="border-b border-[var(--color-border)] hover:bg-[var(--color-bg-tertiary)] transition-colors">
                  <td className="py-4 px-2 font-mono text-sm text-[var(--color-text-primary)]">#{order.id.substring(0, 8)}</td>
                  <td className="py-4 px-2 text-[var(--color-text-primary)]">{order.user.email}</td>
                  <td className="py-4 px-2 font-semibold text-[var(--color-text-primary)]">{formatCurrency(order.totalAmount, order.currency)}</td>
                  <td className="py-4 px-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      {order.status}
                    </span>
                  </td>
                  <td className="py-4 px-2 text-[var(--color-text-secondary)]">{formatDate(order.createdAt)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

interface StatCardProps {
  title: string
  value: string
  icon: React.ElementType
  color: 'green' | 'blue' | 'purple' | 'orange'
}

function StatCard({ title, value, icon: Icon, color }: StatCardProps) {
  const colorClasses = {
    green: 'from-emerald-500 to-teal-600',
    blue: 'from-blue-500 to-indigo-600',
    purple: 'from-purple-500 to-pink-600',
    orange: 'from-orange-500 to-red-600'
  }

  return (
    <div className="card card-hover group">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-[var(--color-text-secondary)] mb-2">{title}</p>
          <p className="text-3xl font-bold text-[var(--color-text-primary)] group-hover:scale-105 transition-transform duration-200">{value}</p>
        </div>
        <div className={`p-4 rounded-2xl bg-gradient-to-br ${colorClasses[color]} shadow-lg group-hover:shadow-xl transition-all duration-200`}>
          <Icon className="h-8 w-8 text-white" />
        </div>
      </div>
    </div>
  )
}



function getStatusBadgeColor(status: string) {
  switch (status) {
    case 'DELIVERED': return 'bg-green-100 text-green-800'
    case 'PAID': return 'bg-blue-100 text-blue-800'
    case 'PENDING': return 'bg-yellow-100 text-yellow-800'
    case 'CANCELLED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function getStatusIcon(status: string) {
  switch (status) {
    case 'DELIVERED': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PAID': return <CheckCircle className="h-3 w-3 mr-1" />
    case 'PENDING': return <Clock className="h-3 w-3 mr-1" />
    case 'CANCELLED': return <XCircle className="h-3 w-3 mr-1" />
    default: return null
  }
}
