# Digital E-commerce System Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration
POSTGRES_DB=ecommerce_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-database-password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-characters

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-from-botfather
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook/telegram

# CORS Configuration
CORS_ORIGIN=https://your-admin-domain.com

# API Configuration
NEXT_PUBLIC_API_URL=https://your-api-domain.com/api

# Production Settings
NODE_ENV=production

# Optional: Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Optional: Cloud Storage (if using cloud instead of local storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Optional: Payment API Keys (for payment verification)
BLOCKCHAIN_INFO_API=https://blockchain.info/q
ETHERSCAN_API_KEY=your-etherscan-api-key
ETHERSCAN_API_URL=https://api.etherscan.io/api
