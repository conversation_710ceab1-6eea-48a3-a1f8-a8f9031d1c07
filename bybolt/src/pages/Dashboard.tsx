import React from 'react';
import StatCard from '../components/dashboard/StatCard';
import OrdersChart from '../components/dashboard/OrdersChart';
import RevenueChart from '../components/dashboard/RevenueChart';
import TopProducts from '../components/dashboard/TopProducts';
import RecentOrders from '../components/dashboard/RecentOrders';
import { BarChart2, ShoppingCart, DollarSign, Users } from 'lucide-react';

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Orders"
          value="156"
          icon={<ShoppingCart size={24} />}
          change={{ value: 12.5, isPositive: true }}
        />
        <StatCard
          title="Total Revenue"
          value="$12,426"
          icon={<DollarSign size={24} />}
          change={{ value: 8.2, isPositive: true }}
        />
        <StatCard
          title="Active Customers"
          value="834"
          icon={<Users size={24} />}
          change={{ value: 4.6, isPositive: true }}
        />
        <StatCard
          title="Conversion Rate"
          value="3.6%"
          icon={<BarChart2 size={24} />}
          change={{ value: 1.2, isPositive: false }}
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RevenueChart />
        <TopProducts />
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <OrdersChart />
        <RecentOrders />
      </div>
    </div>
  );
};

export default Dashboard;