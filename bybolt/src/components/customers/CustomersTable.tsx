import React from 'react';
import Card from '../common/Card';
import Table from '../common/Table';
import Badge from '../common/Badge';
import Button from '../common/Button';
import { Download, MessageSquare, UserPlus } from 'lucide-react';
import { Customer } from '../../types';

const sampleCustomers: Customer[] = [
  {
    id: '1',
    name: '<PERSON>',
    username: 'joh<PERSON><PERSON>',
    telegramId: '123456789',
    phone: '+****************',
    email: '<EMAIL>',
    orders: 5,
    totalSpent: 299.95,
    status: 'active',
    lastActive: '2025-05-10T12:30:00Z',
    joinedAt: '2024-11-15T10:20:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    username: 'jane<PERSON>',
    telegramId: '987654321',
    phone: '+****************',
    email: '<EMAIL>',
    orders: 3,
    totalSpent: 159.97,
    status: 'active',
    lastActive: '2025-05-09T15:45:00Z',
    joinedAt: '2024-12-05T14:10:00Z',
  },
  {
    id: '3',
    name: '<PERSON>',
    username: 'robertj',
    telegramId: '456789123',
    phone: '+****************',
    email: '<EMAIL>',
    orders: 8,
    totalSpent: 725.60,
    status: 'active',
    lastActive: '2025-05-10T09:15:00Z',
    joinedAt: '2024-10-20T11:30:00Z',
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    username: 'sarahw',
    telegramId: '789123456',
    phone: '+****************',
    email: '<EMAIL>',
    orders: 2,
    totalSpent: 89.98,
    status: 'active',
    lastActive: '2025-05-08T16:20:00Z',
    joinedAt: '2025-01-10T09:45:00Z',
  },
  {
    id: '5',
    name: 'Michael Brown',
    username: 'michaelb',
    telegramId: '321654987',
    phone: '+****************',
    email: '<EMAIL>',
    orders: 0,
    totalSpent: 0,
    status: 'blocked',
    lastActive: '2025-04-20T10:30:00Z',
    joinedAt: '2025-02-15T13:25:00Z',
  },
];

const CustomersTable: React.FC = () => {
  const columns = [
    { 
      key: 'name', 
      header: 'Customer', 
      width: 'w-1/5',
      render: (value: string, customer: Customer) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-xs text-gray-500">@{customer.username}</div>
        </div>
      )
    },
    { 
      key: 'email', 
      header: 'Contact', 
      width: 'w-1/5',
      render: (value: string, customer: Customer) => (
        <div>
          <div className="text-sm">{value}</div>
          <div className="text-xs text-gray-500">{customer.phone}</div>
        </div>
      )
    },
    { 
      key: 'orders', 
      header: 'Orders', 
      width: 'w-1/10',
    },
    { 
      key: 'totalSpent', 
      header: 'Total Spent', 
      width: 'w-1/10',
      render: (value: number) => `$${value.toFixed(2)}` 
    },
    { 
      key: 'lastActive', 
      header: 'Last Active', 
      width: 'w-1/5',
      render: (value: string) => new Date(value).toLocaleString() 
    },
    { 
      key: 'status', 
      header: 'Status', 
      width: 'w-1/10',
      render: (value: 'active' | 'blocked') => {
        const statusMap = {
          active: { text: 'Active', variant: 'success' as const },
          blocked: { text: 'Blocked', variant: 'danger' as const },
        };
        
        const status = statusMap[value];
        return <Badge variant={status.variant}>{status.text}</Badge>;
      } 
    },
    {
      key: 'actions',
      header: '',
      width: 'w-1/10',
      render: () => (
        <Button 
          variant="outline" 
          size="sm" 
          icon={<MessageSquare size={16} />}
        >
          Message
        </Button>
      )
    }
  ];

  return (
    <Card 
      title="Customer Management" 
      actions={
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            icon={<Download size={16} />}
          >
            Export
          </Button>
          <Button 
            variant="primary" 
            size="sm" 
            icon={<UserPlus size={16} />}
          >
            Add Customer
          </Button>
        </div>
      }
    >
      <div className="p-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4 flex flex-wrap gap-3">
          <input
            type="text"
            placeholder="Search by name, username, or email..."
            className="flex-1 min-w-[200px] px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="blocked">Blocked</option>
          </select>
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Sort By</option>
            <option value="newest">Newest</option>
            <option value="oldest">Oldest</option>
            <option value="orders">Most Orders</option>
            <option value="spent">Highest Spent</option>
          </select>
        </div>
        
        <Table 
          columns={columns} 
          data={sampleCustomers} 
          onRowClick={(customer) => console.log('Customer clicked:', customer.id)}
        />
        
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to 5 of 5 entries
          </div>
          
          <div className="flex gap-1">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="primary" size="sm">1</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CustomersTable;