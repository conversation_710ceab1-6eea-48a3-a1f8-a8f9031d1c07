import React from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import { Save } from 'lucide-react';
import { ShopSettings } from '../../types';

const sampleSettings: ShopSettings = {
  paymentMethods: [
    { id: '1', name: 'Credit Card', enabled: true },
    { id: '2', name: 'PayPal', enabled: true },
    { id: '3', name: 'Apple Pay', enabled: false },
    { id: '4', name: 'Google Pay', enabled: false },
    { id: '5', name: 'Bitcoin', enabled: false },
  ],
  shippingOptions: [
    { id: '1', name: 'Standard Shipping', price: 5.99, enabled: true },
    { id: '2', name: 'Express Shipping', price: 12.99, enabled: true },
    { id: '3', name: 'Free Shipping (orders over $100)', price: 0, enabled: true },
  ],
  staffAccess: [
    { userId: '1', name: 'Admin User', role: 'admin', permissions: ['all'] },
    { userId: '2', name: 'Manager User', role: 'manager', permissions: ['products', 'orders', 'customers'] },
    { userId: '3', name: 'Support User', role: 'support', permissions: ['orders', 'customers'] },
  ],
  botResponses: [
    { key: 'welcome', message: 'Welcome to our store! How can I help you today?' },
    { key: 'order_confirmation', message: 'Thank you for your order! Your order #{{order_id}} has been confirmed and is being processed.' },
    { key: 'shipping_notification', message: 'Great news! Your order #{{order_id}} has been shipped. You can track it with the following tracking number: {{tracking_number}}' },
    { key: 'customer_support', message: 'I\'m sorry to hear you\'re having an issue. A support representative will assist you shortly.' },
  ],
};

const SettingsPanel: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card title="Payment Methods">
        <div className="p-6 space-y-4">
          {sampleSettings.paymentMethods.map((method) => (
            <div 
              key={method.id} 
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div className="font-medium">{method.name}</div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={method.enabled} 
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </Card>
      
      <Card title="Shipping Options">
        <div className="p-6 space-y-4">
          {sampleSettings.shippingOptions.map((option) => (
            <div 
              key={option.id} 
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div>
                <div className="font-medium">{option.name}</div>
                <div className="text-sm text-gray-500">
                  {option.price === 0 ? 'Free' : `$${option.price.toFixed(2)}`}
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={option.enabled} 
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </Card>
      
      <Card title="Staff Access">
        <div className="p-6 space-y-4">
          {sampleSettings.staffAccess.map((staff) => (
            <div 
              key={staff.userId} 
              className="p-3 border border-gray-200 rounded-lg"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium">{staff.name}</div>
                <Badge 
                  variant={
                    staff.role === 'admin' 
                      ? 'primary' 
                      : staff.role === 'manager' 
                        ? 'secondary' 
                        : 'info'
                  }
                >
                  {staff.role.charAt(0).toUpperCase() + staff.role.slice(1)}
                </Badge>
              </div>
              
              <div className="text-sm text-gray-500">
                Permissions: {staff.permissions.map((p) => 
                  p.charAt(0).toUpperCase() + p.slice(1)
                ).join(', ')}
              </div>
            </div>
          ))}
          
          <Button 
            variant="outline" 
            className="mt-2"
          >
            Add Staff Member
          </Button>
        </div>
      </Card>
      
      <Card title="Bot Responses">
        <div className="p-6 space-y-4">
          {sampleSettings.botResponses.map((response) => (
            <div 
              key={response.key} 
              className="space-y-2"
            >
              <div className="font-medium">{response.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={2}
                defaultValue={response.message}
              />
            </div>
          ))}
          
          <Button 
            variant="primary" 
            className="mt-4"
            icon={<Save size={16} />}
          >
            Save Changes
          </Button>
        </div>
      </Card>
    </div>
  );
};

// Add missing imports
import Badge from '../common/Badge';

export default SettingsPanel;