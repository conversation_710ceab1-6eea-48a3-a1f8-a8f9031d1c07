import React from 'react';
import Card from '../common/Card';

const OrdersChart: React.FC = () => {
  // This is a placeholder for the chart
  // In a real implementation, you would use a charting library like Chart.js or Recharts
  
  return (
    <Card title="Orders Overview">
      <div className="p-6">
        {/* Sample chart representation */}
        <div className="h-64 flex flex-col">
          <div className="flex justify-between mb-4">
            <div className="text-sm font-medium">
              <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
              New Orders
            </div>
            <div className="text-sm font-medium">
              <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
              Completed
            </div>
            <div className="text-sm font-medium">
              <span className="inline-block w-3 h-3 bg-red-500 rounded-full mr-2"></span>
              Cancelled
            </div>
          </div>
          
          <div className="flex-1 flex items-end">
            {/* Chart bars */}
            {[
              { day: 'Mon', new: 12, completed: 8, cancelled: 2 },
              { day: 'Tue', new: 18, completed: 15, cancelled: 1 },
              { day: 'Wed', new: 15, completed: 12, cancelled: 2 },
              { day: 'Thu', new: 25, completed: 20, cancelled: 3 },
              { day: 'Fri', new: 30, completed: 22, cancelled: 4 },
              { day: 'Sat', new: 28, completed: 25, cancelled: 2 },
              { day: 'Sun', new: 20, completed: 18, cancelled: 1 },
            ].map((data, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col items-center space-y-1">
                  <div 
                    className="w-6 bg-red-500 rounded-t" 
                    style={{ height: `${data.cancelled * 2}px` }}
                  ></div>
                  <div 
                    className="w-6 bg-green-500" 
                    style={{ height: `${data.completed * 2}px` }}
                  ></div>
                  <div 
                    className="w-6 bg-blue-500 rounded-t" 
                    style={{ height: `${data.new * 2}px` }}
                  ></div>
                </div>
                <div className="mt-2 text-xs text-gray-500">{data.day}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default OrdersChart;