import React from 'react';
import Card from '../common/Card';
import Table from '../common/Table';
import Badge from '../common/Badge';
import { Order } from '../../types';

const sampleOrders: Order[] = [
  {
    id: 'ORD-001',
    customerId: '1',
    customerName: '<PERSON>',
    status: 'new',
    items: [{ productId: '1', productName: 'Headphones', quantity: 1, price: 99, totalPrice: 99 }],
    totalAmount: 99,
    shippingAddress: '123 Main St, New York, NY 10001',
    paymentMethod: 'Credit Card',
    createdAt: '2025-05-10T14:30:00Z',
    updatedAt: '2025-05-10T14:30:00Z',
  },
  {
    id: 'ORD-002',
    customerId: '2',
    customerName: '<PERSON>',
    status: 'processing',
    items: [{ productId: '2', productName: 'Smartphone', quantity: 1, price: 499, totalPrice: 499 }],
    totalAmount: 499,
    shippingAddress: '456 Park Ave, Boston, MA 02108',
    paymentMethod: 'PayPal',
    createdAt: '2025-05-09T11:15:00Z',
    updatedAt: '2025-05-10T09:20:00Z',
  },
  {
    id: 'ORD-003',
    customerId: '3',
    customerName: 'Robert Johnson',
    status: 'completed',
    items: [{ productId: '3', productName: 'Smart Watch', quantity: 1, price: 199, totalPrice: 199 }],
    totalAmount: 199,
    shippingAddress: '789 Elm St, Chicago, IL 60007',
    paymentMethod: 'Credit Card',
    trackingNumber: 'TRK12345',
    createdAt: '2025-05-08T16:45:00Z',
    updatedAt: '2025-05-10T10:30:00Z',
  },
  {
    id: 'ORD-004',
    customerId: '4',
    customerName: 'Sarah Wilson',
    status: 'new',
    items: [{ productId: '4', productName: 'Bluetooth Speaker', quantity: 2, price: 79, totalPrice: 158 }],
    totalAmount: 158,
    shippingAddress: '101 Pine St, Seattle, WA 98101',
    paymentMethod: 'Credit Card',
    createdAt: '2025-05-10T09:20:00Z',
    updatedAt: '2025-05-10T09:20:00Z',
  },
  {
    id: 'ORD-005',
    customerId: '5',
    customerName: 'Michael Brown',
    status: 'cancelled',
    items: [{ productId: '5', productName: 'Wireless Earbuds', quantity: 1, price: 129, totalPrice: 129 }],
    totalAmount: 129,
    shippingAddress: '202 Oak St, San Francisco, CA 94107',
    paymentMethod: 'PayPal',
    createdAt: '2025-05-07T13:10:00Z',
    updatedAt: '2025-05-09T08:15:00Z',
  },
];

const RecentOrders: React.FC = () => {
  const columns = [
    { key: 'id', header: 'Order ID', width: 'w-1/6' },
    { key: 'customerName', header: 'Customer', width: 'w-1/4' },
    { 
      key: 'totalAmount', 
      header: 'Amount', 
      width: 'w-1/6',
      render: (value: number) => `$${value}` 
    },
    { 
      key: 'createdAt', 
      header: 'Date', 
      width: 'w-1/4',
      render: (value: string) => new Date(value).toLocaleString() 
    },
    { 
      key: 'status', 
      header: 'Status', 
      width: 'w-1/6',
      render: (value: 'new' | 'processing' | 'completed' | 'cancelled') => {
        const statusMap = {
          new: { text: 'New', variant: 'primary' as const },
          processing: { text: 'Processing', variant: 'warning' as const },
          completed: { text: 'Completed', variant: 'success' as const },
          cancelled: { text: 'Cancelled', variant: 'danger' as const },
        };
        
        const status = statusMap[value];
        return <Badge variant={status.variant}>{status.text}</Badge>;
      } 
    },
  ];

  return (
    <Card 
      title="Recent Orders" 
      className="col-span-2"
      actions={
        <a href="#\" className="text-sm text-blue-600 hover:text-blue-800">View All</a>
      }
    >
      <Table 
        columns={columns} 
        data={sampleOrders} 
        onRowClick={(order) => console.log('Order clicked:', order.id)}
      />
    </Card>
  );
};

export default RecentOrders;