import React from 'react';
import Card from '../common/Card';

const RevenueChart: React.FC = () => {
  // This is a placeholder for the chart
  // In a real implementation, you would use a charting library like Chart.js or Recharts
  
  return (
    <Card title="Revenue" className="col-span-2">
      <div className="p-6">
        {/* Sample line chart representation */}
        <div className="h-64 flex flex-col">
          <div className="flex justify-between mb-4">
            <div className="text-sm font-medium">
              <span className="inline-block w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
              Revenue
            </div>
            <div className="text-sm font-medium">
              <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
              Profit
            </div>
          </div>
          
          <div className="flex-1 relative">
            {/* Y-axis labels */}
            <div className="absolute left-0 top-0 bottom-0 flex flex-col justify-between">
              <span className="text-xs text-gray-400">$5,000</span>
              <span className="text-xs text-gray-400">$4,000</span>
              <span className="text-xs text-gray-400">$3,000</span>
              <span className="text-xs text-gray-400">$2,000</span>
              <span className="text-xs text-gray-400">$1,000</span>
              <span className="text-xs text-gray-400">$0</span>
            </div>
            
            {/* Chart area */}
            <div className="ml-10 h-full flex items-end">
              {/* Line chart representation */}
              <svg className="w-full h-full">
                {/* Horizontal grid lines */}
                {[0, 1, 2, 3, 4, 5].map((i) => (
                  <line
                    key={i}
                    x1="0"
                    y1={i * 20 + "%"}
                    x2="100%"
                    y2={i * 20 + "%"}
                    stroke="#e5e7eb"
                    strokeWidth="1"
                  />
                ))}
                
                {/* Revenue line */}
                <polyline
                  points="0,80% 14.28%,75% 28.57%,65% 42.85%,50% 57.14%,40% 71.42%,35% 85.71%,25% 100%,30%"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                />
                
                {/* Profit line */}
                <polyline
                  points="0,90% 14.28%,85% 28.57%,80% 42.85%,70% 57.14%,65% 71.42%,60% 85.71%,55% 100%,60%"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="2"
                />
                
                {/* Data points for revenue */}
                {[80, 75, 65, 50, 40, 35, 25, 30].map((value, i) => (
                  <circle
                    key={`revenue-${i}`}
                    cx={i * 14.28 + "%"}
                    cy={value + "%"}
                    r="4"
                    fill="#3b82f6"
                  />
                ))}
                
                {/* Data points for profit */}
                {[90, 85, 80, 70, 65, 60, 55, 60].map((value, i) => (
                  <circle
                    key={`profit-${i}`}
                    cx={i * 14.28 + "%"}
                    cy={value + "%"}
                    r="4"
                    fill="#10b981"
                  />
                ))}
              </svg>
            </div>
          </div>
          
          {/* X-axis labels */}
          <div className="flex justify-between mt-2">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, i) => (
              <span key={i} className="text-xs text-gray-400">{day}</span>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default RevenueChart;