import React from 'react';
import Card from '../common/Card';
import { TopProduct } from '../../types';

const sampleTopProducts: TopProduct[] = [
  { id: '1', name: 'Premium Headphones', sold: 48, revenue: 4800 },
  { id: '2', name: 'Wireless Earbuds', sold: 42, revenue: 3360 },
  { id: '3', name: 'Smartphone Case', sold: 36, revenue: 720 },
  { id: '4', name: 'Smart Watch', sold: 30, revenue: 3600 },
  { id: '5', name: 'Power Bank 10000mAh', sold: 24, revenue: 960 },
];

const TopProducts: React.FC = () => {
  return (
    <Card title="Top Products">
      <div className="px-6 py-4">
        <div className="space-y-3">
          {sampleTopProducts.map((product, index) => (
            <div 
              key={product.id} 
              className="flex items-center justify-between py-2"
            >
              <div className="flex items-center">
                <span className="w-5 text-gray-500 text-sm">{index + 1}.</span>
                <div>
                  <p className="text-sm font-medium text-gray-800">{product.name}</p>
                  <p className="text-xs text-gray-500">{product.sold} sold</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-800">${product.revenue}</p>
                <p className="text-xs text-gray-500">Revenue</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default TopProducts;