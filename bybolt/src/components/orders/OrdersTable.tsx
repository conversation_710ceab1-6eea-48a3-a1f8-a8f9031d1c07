import React, { useState } from 'react';
import Card from '../common/Card';
import Table from '../common/Table';
import Badge from '../common/Badge';
import Button from '../common/Button';
import { Download, Filter } from 'lucide-react';
import { Order } from '../../types';

const sampleOrders: Order[] = [
  {
    id: 'ORD-001',
    customerId: '1',
    customerName: '<PERSON>',
    status: 'new',
    items: [{ productId: '1', productName: 'Headphones', quantity: 1, price: 99, totalPrice: 99 }],
    totalAmount: 99,
    shippingAddress: '123 Main St, New York, NY 10001',
    paymentMethod: 'Credit Card',
    createdAt: '2025-05-10T14:30:00Z',
    updatedAt: '2025-05-10T14:30:00Z',
  },
  {
    id: 'ORD-002',
    customerId: '2',
    customerName: 'Jane <PERSON>',
    status: 'processing',
    items: [{ productId: '2', productName: 'Smartphone', quantity: 1, price: 499, totalPrice: 499 }],
    totalAmount: 499,
    shippingAddress: '456 Park Ave, Boston, MA 02108',
    paymentMethod: 'PayPal',
    createdAt: '2025-05-09T11:15:00Z',
    updatedAt: '2025-05-10T09:20:00Z',
  },
  {
    id: 'ORD-003',
    customerId: '3',
    customerName: 'Robert Johnson',
    status: 'completed',
    items: [{ productId: '3', productName: 'Smart Watch', quantity: 1, price: 199, totalPrice: 199 }],
    totalAmount: 199,
    shippingAddress: '789 Elm St, Chicago, IL 60007',
    paymentMethod: 'Credit Card',
    trackingNumber: 'TRK12345',
    createdAt: '2025-05-08T16:45:00Z',
    updatedAt: '2025-05-10T10:30:00Z',
  },
  {
    id: 'ORD-004',
    customerId: '4',
    customerName: 'Sarah Wilson',
    status: 'new',
    items: [{ productId: '4', productName: 'Bluetooth Speaker', quantity: 2, price: 79, totalPrice: 158 }],
    totalAmount: 158,
    shippingAddress: '101 Pine St, Seattle, WA 98101',
    paymentMethod: 'Credit Card',
    createdAt: '2025-05-10T09:20:00Z',
    updatedAt: '2025-05-10T09:20:00Z',
  },
  {
    id: 'ORD-005',
    customerId: '5',
    customerName: 'Michael Brown',
    status: 'cancelled',
    items: [{ productId: '5', productName: 'Wireless Earbuds', quantity: 1, price: 129, totalPrice: 129 }],
    totalAmount: 129,
    shippingAddress: '202 Oak St, San Francisco, CA 94107',
    paymentMethod: 'PayPal',
    createdAt: '2025-05-07T13:10:00Z',
    updatedAt: '2025-05-09T08:15:00Z',
  },
  {
    id: 'ORD-006',
    customerId: '6',
    customerName: 'Emily Davis',
    status: 'completed',
    items: [
      { productId: '6', productName: 'Phone Case', quantity: 1, price: 25, totalPrice: 25 },
      { productId: '7', productName: 'Screen Protector', quantity: 2, price: 15, totalPrice: 30 }
    ],
    totalAmount: 55,
    shippingAddress: '303 Maple St, Dallas, TX 75001',
    paymentMethod: 'Credit Card',
    trackingNumber: 'TRK67890',
    createdAt: '2025-05-06T10:05:00Z',
    updatedAt: '2025-05-08T14:25:00Z',
  },
  {
    id: 'ORD-007',
    customerId: '7',
    customerName: 'David Wilson',
    status: 'processing',
    items: [{ productId: '8', productName: 'Smart TV', quantity: 1, price: 699, totalPrice: 699 }],
    totalAmount: 699,
    shippingAddress: '404 Cedar St, Atlanta, GA 30301',
    paymentMethod: 'Credit Card',
    createdAt: '2025-05-09T08:50:00Z',
    updatedAt: '2025-05-10T11:40:00Z',
  },
];

const OrdersTable: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  
  const filteredOrders = selectedStatus 
    ? sampleOrders.filter(order => order.status === selectedStatus)
    : sampleOrders;
  
  const columns = [
    { key: 'id', header: 'Order ID', width: 'w-1/6' },
    { key: 'customerName', header: 'Customer', width: 'w-1/5' },
    { 
      key: 'totalAmount', 
      header: 'Amount', 
      width: 'w-1/8',
      render: (value: number) => `$${value.toFixed(2)}` 
    },
    { 
      key: 'createdAt', 
      header: 'Date', 
      width: 'w-1/5',
      render: (value: string) => new Date(value).toLocaleString() 
    },
    { 
      key: 'paymentMethod', 
      header: 'Payment', 
      width: 'w-1/8',
    },
    { 
      key: 'status', 
      header: 'Status', 
      width: 'w-1/8',
      render: (value: 'new' | 'processing' | 'completed' | 'cancelled') => {
        const statusMap = {
          new: { text: 'New', variant: 'primary' as const },
          processing: { text: 'Processing', variant: 'warning' as const },
          completed: { text: 'Completed', variant: 'success' as const },
          cancelled: { text: 'Cancelled', variant: 'danger' as const },
        };
        
        const status = statusMap[value];
        return <Badge variant={status.variant}>{status.text}</Badge>;
      } 
    },
  ];

  return (
    <Card 
      title="Order Processing" 
      actions={
        <Button 
          variant="outline" 
          size="sm" 
          icon={<Download size={16} />}
        >
          Export Orders
        </Button>
      }
    >
      <div className="p-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4 flex flex-wrap gap-3">
          <input
            type="text"
            placeholder="Search by order ID or customer..."
            className="flex-1 min-w-[200px] px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          
          <select 
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="">All Status</option>
            <option value="new">New</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
          
          <Button 
            variant="outline" 
            size="sm" 
            icon={<Filter size={16} />}
          >
            More Filters
          </Button>
        </div>
        
        <Table 
          columns={columns} 
          data={filteredOrders}
          onRowClick={(order) => console.log('Order clicked:', order.id)}
        />
        
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to {filteredOrders.length} of {filteredOrders.length} entries
          </div>
          
          <div className="flex gap-1">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="primary" size="sm">1</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default OrdersTable;