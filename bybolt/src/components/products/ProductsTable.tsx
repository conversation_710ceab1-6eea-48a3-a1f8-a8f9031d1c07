import React from 'react';
import Card from '../common/Card';
import Table from '../common/Table';
import Badge from '../common/Badge';
import Button from '../common/Button';
import { Plus, Download, Upload } from 'lucide-react';
import { Product } from '../../types';

const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Headphones',
    description: 'Premium noise-cancelling wireless headphones with 30-hour battery life.',
    price: 99.99,
    images: ['https://images.pexels.com/photos/577769/pexels-photo-577769.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Electronics',
    inventory: 45,
    status: 'available',
    createdAt: '2025-01-15T10:30:00Z',
    updatedAt: '2025-05-01T14:20:00Z',
  },
  {
    id: '2',
    name: 'Smart Watch',
    description: 'Fitness and health tracking smartwatch with heart rate monitor and GPS.',
    price: 199.99,
    images: ['https://images.pexels.com/photos/437037/pexels-photo-437037.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Electronics',
    inventory: 28,
    status: 'available',
    createdAt: '2025-02-10T09:15:00Z',
    updatedAt: '2025-05-05T11:30:00Z',
  },
  {
    id: '3',
    name: 'Bluetooth Speaker',
    description: 'Portable waterproof Bluetooth speaker with 24-hour battery life.',
    price: 79.99,
    images: ['https://images.pexels.com/photos/1279107/pexels-photo-1279107.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Electronics',
    inventory: 60,
    status: 'available',
    createdAt: '2025-03-05T15:45:00Z',
    updatedAt: '2025-04-20T13:10:00Z',
  },
  {
    id: '4',
    name: 'Phone Case',
    description: 'Durable and stylish phone case with shock absorption technology.',
    price: 19.99,
    images: ['https://images.pexels.com/photos/1294886/pexels-photo-1294886.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Accessories',
    inventory: 120,
    status: 'available',
    createdAt: '2025-01-20T11:00:00Z',
    updatedAt: '2025-04-15T09:50:00Z',
  },
  {
    id: '5',
    name: 'Wireless Earbuds',
    description: 'True wireless earbuds with active noise cancellation and touch controls.',
    price: 129.99,
    images: ['https://images.pexels.com/photos/3780681/pexels-photo-3780681.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Electronics',
    inventory: 35,
    status: 'available',
    createdAt: '2025-02-25T14:20:00Z',
    updatedAt: '2025-05-03T10:15:00Z',
  },
  {
    id: '6',
    name: 'Power Bank',
    description: '20000mAh high-capacity power bank with fast charging technology.',
    price: 49.99,
    images: ['https://images.pexels.com/photos/47856/rolex-wrist-watch-time-gears-47856.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'],
    category: 'Electronics',
    inventory: 0,
    status: 'unavailable',
    createdAt: '2025-03-10T16:30:00Z',
    updatedAt: '2025-04-25T15:40:00Z',
  },
];

const ProductsTable: React.FC = () => {
  const columns = [
    { 
      key: 'name', 
      header: 'Product', 
      width: 'w-1/4',
      render: (value: string, product: Product) => (
        <div className="flex items-center">
          <div className="w-10 h-10 flex-shrink-0 mr-3">
            <img
              src={product.images[0]}
              alt={value}
              className="w-full h-full object-cover rounded"
            />
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-xs text-gray-500">ID: {product.id}</div>
          </div>
        </div>
      )
    },
    { 
      key: 'price', 
      header: 'Price', 
      width: 'w-1/6',
      render: (value: number) => `$${value.toFixed(2)}` 
    },
    { 
      key: 'category', 
      header: 'Category', 
      width: 'w-1/6',
    },
    { 
      key: 'inventory', 
      header: 'Inventory', 
      width: 'w-1/6',
      render: (value: number) => (
        <span className={value === 0 ? 'text-red-600 font-medium' : ''}>{value}</span>
      )
    },
    { 
      key: 'status', 
      header: 'Status', 
      width: 'w-1/6',
      render: (value: 'available' | 'unavailable') => {
        const statusMap = {
          available: { text: 'Available', variant: 'success' as const },
          unavailable: { text: 'Unavailable', variant: 'danger' as const },
        };
        
        const status = statusMap[value];
        return <Badge variant={status.variant}>{status.text}</Badge>;
      } 
    },
  ];

  return (
    <Card 
      title="Product Management" 
      actions={
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            icon={<Download size={16} />}
          >
            Export
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            icon={<Upload size={16} />}
          >
            Import
          </Button>
          <Button 
            variant="primary" 
            size="sm" 
            icon={<Plus size={16} />}
          >
            Add Product
          </Button>
        </div>
      }
    >
      <div className="p-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4 flex flex-wrap gap-3">
          <input
            type="text"
            placeholder="Search products..."
            className="flex-1 min-w-[200px] px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">All Categories</option>
            <option value="Electronics">Electronics</option>
            <option value="Accessories">Accessories</option>
            <option value="Clothing">Clothing</option>
          </select>
          
          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">All Status</option>
            <option value="available">Available</option>
            <option value="unavailable">Unavailable</option>
          </select>
        </div>
        
        <Table 
          columns={columns} 
          data={sampleProducts} 
          onRowClick={(product) => console.log('Product clicked:', product.id)}
        />
        
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to 6 of 6 entries
          </div>
          
          <div className="flex gap-1">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="primary" size="sm">1</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ProductsTable;