import React from 'react';
import { useAppContext } from '../../context/AppContext';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart2,
  Settings,
  ChevronLeft,
  ChevronRight,
  MessageCircle,
  LogOut,
  Bot,
} from 'lucide-react';

interface NavItemProps {
  icon: React.ReactNode;
  text: string;
  active: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, text, active, onClick }) => (
  <div
    className={`flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer transition-colors ${
      active
        ? 'bg-blue-100 text-blue-900'
        : 'text-gray-600 hover:bg-gray-100'
    }`}
    onClick={onClick}
  >
    <div className="text-lg">{icon}</div>
    <span className="font-medium transition-opacity duration-200">
      {text}
    </span>
  </div>
);

const Sidebar: React.FC = () => {
  const { sidebarOpen, toggleSidebar, activeSection, setActiveSection } =
    useAppContext();

  const navItems = [
    {
      icon: <LayoutDashboard size={20} />,
      text: 'Dashboard',
      id: 'dashboard',
    },
    {
      icon: <Package size={20} />,
      text: 'Products',
      id: 'products',
    },
    {
      icon: <ShoppingCart size={20} />,
      text: 'Orders',
      id: 'orders',
    },
    {
      icon: <Users size={20} />,
      text: 'Customers',
      id: 'customers',
    },
    {
      icon: <MessageCircle size={20} />,
      text: 'Support',
      id: 'support',
    },
    {
      icon: <BarChart2 size={20} />,
      text: 'Analytics',
      id: 'analytics',
    },
    {
      icon: <Settings size={20} />,
      text: 'Settings',
      id: 'settings',
    },
  ];

  return (
    <div
      className={`h-screen fixed top-0 left-0 bg-white border-r border-gray-200 transition-all duration-300 shadow-sm z-30 ${
        sidebarOpen ? 'w-64' : 'w-16'
      }`}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className={`flex items-center gap-3 ${sidebarOpen ? '' : 'hidden'}`}>
            <Bot size={24} className="text-blue-800" />
            <span className="font-bold text-xl text-blue-900">TeleShop</span>
          </div>
          <button
            className="p-1 rounded-lg text-gray-500 hover:bg-gray-100"
            onClick={toggleSidebar}
          >
            {sidebarOpen ? (
              <ChevronLeft size={20} />
            ) : (
              <ChevronRight size={20} />
            )}
          </button>
        </div>

        <div className="flex-1 py-4 overflow-y-auto">
          <div className="space-y-1 px-2">
            {navItems.map((item) => (
              <NavItem
                key={item.id}
                icon={item.icon}
                text={sidebarOpen ? item.text : ''}
                active={activeSection === item.id}
                onClick={() => setActiveSection(item.id)}
              />
            ))}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200">
          <div
            className="flex items-center gap-3 px-4 py-3 rounded-lg cursor-pointer text-red-600 hover:bg-red-50"
          >
            <LogOut size={20} />
            <span className={`font-medium ${sidebarOpen ? '' : 'hidden'}`}>
              Logout
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;