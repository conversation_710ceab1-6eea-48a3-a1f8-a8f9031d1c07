import React from 'react';
import { Bell, Search, User } from 'lucide-react';
import { useAppContext } from '../../context/AppContext';

interface SectionTitleMap {
  [key: string]: string;
}

const Header: React.FC = () => {
  const { activeSection } = useAppContext();

  const sectionTitles: SectionTitleMap = {
    dashboard: 'Dashboard',
    products: 'Product Management',
    orders: 'Order Processing',
    customers: 'Customer Management',
    support: 'Customer Support',
    analytics: 'Analytics Dashboard',
    settings: 'Shop Settings',
  };

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
      <h1 className="text-xl font-semibold text-gray-800">
        {sectionTitles[activeSection] || 'Dashboard'}
      </h1>
      
      <div className="flex items-center gap-6">
        <div className="relative hidden md:block">
          <input
            type="text"
            placeholder="Search..."
            className="py-2 pl-10 pr-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64 text-sm"
          />
          <Search
            size={18}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          />
        </div>
        
        <div className="flex items-center gap-3">
          <button className="relative p-2 text-gray-500 hover:bg-gray-100 rounded-full">
            <Bell size={20} />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          
          <div className="flex items-center gap-3 border-l pl-3 border-gray-200">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-800">
              <User size={18} />
            </div>
            <div className="hidden md:block">
              <p className="text-sm font-medium">Admin User</p>
              <p className="text-xs text-gray-500">Shop Manager</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;