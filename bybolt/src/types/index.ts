export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  inventory: number;
  status: 'available' | 'unavailable';
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  productCount: number;
}

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  status: 'new' | 'processing' | 'completed' | 'cancelled';
  items: OrderItem[];
  totalAmount: number;
  shippingAddress: string;
  paymentMethod: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

export interface Customer {
  id: string;
  name: string;
  username: string;
  telegramId: string;
  phone?: string;
  email?: string;
  orders: number;
  totalSpent: number;
  status: 'active' | 'blocked';
  lastActive: string;
  joinedAt: string;
}

export interface SalesMetric {
  date: string;
  orders: number;
  revenue: number;
  profit: number;
}

export interface TopProduct {
  id: string;
  name: string;
  sold: number;
  revenue: number;
}

export interface CustomerEngagement {
  date: string;
  newCustomers: number;
  activeCustomers: number;
  messagesSent: number;
}

export interface ShopSettings {
  paymentMethods: PaymentMethod[];
  shippingOptions: ShippingOption[];
  staffAccess: StaffAccess[];
  botResponses: BotResponse[];
}

export interface PaymentMethod {
  id: string;
  name: string;
  enabled: boolean;
}

export interface ShippingOption {
  id: string;
  name: string;
  price: number;
  enabled: boolean;
}

export interface StaffAccess {
  userId: string;
  name: string;
  role: 'admin' | 'manager' | 'support';
  permissions: string[];
}

export interface BotResponse {
  key: string;
  message: string;
}

export type DateRange = 'today' | 'week' | 'month' | 'year' | 'custom';