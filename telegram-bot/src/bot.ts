import { Telegraf, Context, Markup } from 'telegraf';
import dotenv from 'dotenv';
import { ApiService } from './services/apiService';
import { UserSession } from './types/session';
import { handleStart } from './handlers/start';
import { handleProducts } from './handlers/products';
import { handleCart } from './handlers/cart';
import { handleOrders } from './handlers/orders';
import { handleProfile } from './handlers/profile';

dotenv.config();

// Extend Context with session
interface BotContext extends Context {
  session?: UserSession;
}

class TelegramBot {
  private bot: Telegraf<BotContext>;
  private apiService: ApiService;
  private sessions: Map<number, UserSession> = new Map();

  constructor() {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    if (!token) {
      throw new Error('TELEGRAM_BOT_TOKEN is required');
    }

    this.bot = new Telegraf<BotContext>(token);
    this.apiService = new ApiService(process.env.API_BASE_URL || 'http://localhost:3001/api');

    this.setupMiddleware();
    this.setupHandlers();
  }

  private setupMiddleware() {
    // Session middleware
    this.bot.use((ctx, next) => {
      const userId = ctx.from?.id;
      if (userId) {
        if (!this.sessions.has(userId)) {
          this.sessions.set(userId, {
            userId,
            cart: { items: [], totalAmount: 0, currency: 'USD' },
            currentPage: 1,
            currentCategory: null,
            isAuthenticated: false,
            user: null
          });
        }
        ctx.session = this.sessions.get(userId);
      }
      return next();
    });

    // Error handling
    this.bot.catch((err, ctx) => {
      console.error('Bot error:', err);
      ctx.reply('❌ An error occurred. Please try again later.');
    });
  }

  private setupHandlers() {
    // Start command
    this.bot.start(handleStart(this.apiService));

    // Main menu commands
    this.bot.command('products', handleProducts(this.apiService));
    this.bot.command('cart', handleCart(this.apiService));
    this.bot.command('orders', handleOrders(this.apiService));
    this.bot.command('profile', handleProfile(this.apiService));

    // Callback query handlers
    this.bot.action(/^products:(.+)$/, handleProducts(this.apiService));
    this.bot.action(/^product:(.+)$/, handleProducts(this.apiService));
    this.bot.action(/^cart:(.+)$/, handleCart(this.apiService));
    this.bot.action(/^order:(.+)$/, handleOrders(this.apiService));
    this.bot.action(/^profile:(.+)$/, handleProfile(this.apiService));

    // Main menu buttons
    this.bot.hears('🛍️ Products', handleProducts(this.apiService));
    this.bot.hears('🛒 Cart', handleCart(this.apiService));
    this.bot.hears('📦 My Orders', handleOrders(this.apiService));
    this.bot.hears('👤 Profile', handleProfile(this.apiService));
    this.bot.hears('🏠 Main Menu', (ctx) => {
      ctx.reply('🏠 Main Menu', this.getMainMenuKeyboard());
    });

    // Login command
    this.bot.command('login', async (ctx) => {
      const args = ctx.message.text.split(' ').slice(1);
      if (args.length < 2) {
        await ctx.reply('Usage: /login email password\n\n⚠️ Please delete your message after sending for security.');
        return;
      }

      const [email, password] = args;

      try {
        const loginResponse = await this.apiService.loginUser(email, password);

        if (loginResponse.success && loginResponse.data) {
          const { user, tokens } = loginResponse.data;

          if (ctx.session) {
            ctx.session.isAuthenticated = true;
            ctx.session.user = user;
            ctx.session.accessToken = tokens.accessToken;
          }

          this.apiService.setAuthToken(tokens.accessToken);

          // Link Telegram account
          await this.apiService.linkTelegramAccount(user.id, ctx.from?.id.toString() || '');

          await ctx.reply(
            `✅ *Login Successful!*\n\nWelcome back, ${user.username || user.email}!\n\n🔗 Your Telegram account has been linked.`,
            { parse_mode: 'Markdown' }
          );
        } else {
          await ctx.reply('❌ Invalid email or password. Please try again.');
        }
      } catch (error) {
        console.error('Login error:', error);
        await ctx.reply('❌ Login failed. Please check your credentials and try again.');
      }
    });

    // Register command
    this.bot.command('register', async (ctx) => {
      const args = ctx.message.text.split(' ').slice(1);
      if (args.length < 2) {
        await ctx.reply('Usage: /register email password [username]\n\n⚠️ Please delete your message after sending for security.');
        return;
      }

      const [email, password, username] = args;

      try {
        const registerResponse = await this.apiService.registerUser(email, password, username);

        if (registerResponse.success && registerResponse.data) {
          const { user, tokens } = registerResponse.data;

          if (ctx.session) {
            ctx.session.isAuthenticated = true;
            ctx.session.user = user;
            ctx.session.accessToken = tokens.accessToken;
          }

          this.apiService.setAuthToken(tokens.accessToken);

          // Link Telegram account
          await this.apiService.linkTelegramAccount(user.id, ctx.from?.id.toString() || '');

          await ctx.reply(
            `✅ *Registration Successful!*\n\nWelcome, ${user.username || user.email}!\n\n🔗 Your Telegram account has been linked.\n🎉 You can now start shopping!`,
            { parse_mode: 'Markdown' }
          );
        } else {
          await ctx.reply('❌ Registration failed. Please check your details and try again.');
        }
      } catch (error) {
        console.error('Registration error:', error);
        await ctx.reply('❌ Registration failed. The email might already be in use.');
      }
    });

    // Help command
    this.bot.help((ctx) => {
      const helpText = `
🤖 *Digital Store Bot Help*

*Available Commands:*
/start - Start the bot and see main menu
/login - Login to your account
/register - Create a new account
/products - Browse products
/cart - View your shopping cart
/orders - View your order history
/profile - Manage your profile
/help - Show this help message

*How to use:*
1. Register or login to your account
2. Browse products using the Products menu
3. Add items to your cart
4. Checkout with cryptocurrency
5. Download your purchased items

*Payment Methods:*
• Bitcoin (BTC)
• Ethereum (ETH)
• USDT
• Litecoin (LTC)

*Support:*
If you need help, contact our support team.
      `;
      ctx.replyWithMarkdown(helpText);
    });
  }

  private getMainMenuKeyboard() {
    return Markup.keyboard([
      ['🛍️ Products', '🛒 Cart'],
      ['📦 My Orders', '👤 Profile'],
      ['❓ Help']
    ]).resize();
  }

  public async start() {
    try {
      await this.bot.launch();
      console.log('🤖 Telegram bot started successfully');

      // Set webhook if URL is provided
      if (process.env.TELEGRAM_WEBHOOK_URL) {
        await this.bot.telegram.setWebhook(process.env.TELEGRAM_WEBHOOK_URL);
        console.log('📡 Webhook set:', process.env.TELEGRAM_WEBHOOK_URL);
      }
    } catch (error) {
      console.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  public async stop() {
    await this.bot.stop();
    console.log('🤖 Telegram bot stopped');
  }
}

// Start the bot
const bot = new TelegramBot();

// Graceful shutdown
process.once('SIGINT', () => bot.stop());
process.once('SIGTERM', () => bot.stop());

// Start the bot
bot.start().catch(console.error);

export default bot;
